# GitHub Actions CI/CD Setup

This repository uses GitHub Actions for continuous integration and deployment. The workflow is converted from the original GitLab CI/CD pipeline.

## Workflow Overview

The CI/CD pipeline consists of the following stages:

1. **Test** - Runs unit tests (triggered on pull requests)
2. **Bump** - Automatically bumps version on main branch
3. **Prepare** - Prepares build environment for release branches
4. **Build** - Builds and pushes Docker image to ECR
5. **Deploy** - Deploys to Kubernetes cluster
6. **Backup** - Creates git tags for releases

## Required GitHub Secrets

You need to configure the following secrets in your GitHub repository settings:

### Git Configuration
- `CI_EMAIL` - Email for git commits
- `CI_USER` - Username for git commits  
- `CI_USER_ACCESS_TOKEN` - GitHub personal access token with repo permissions
- `DEPLOYMENT_SSH_PRIVATE_KEY` - SSH private key for deployments

### AWS ECR Configuration
- `ECR_AWS_REGION` - AWS region for ECR (e.g., us-east-1)
- `ECR_AWS_PROFILE` - AWS profile name for ECR
- `ECR_AWS_ACCESS_KEY_ID` - AWS access key for ECR
- `ECR_AWS_SECRET_ACCESS_KEY` - AWS secret key for ECR
- `ECR_DOMAIN` - ECR domain (e.g., 123456789012.dkr.ecr.us-east-1.amazonaws.com)
- `ECR_REPO_NAME` - ECR repository name

### Docker Build Configuration
- `DOCKER_AWS_ACCESS_KEY_ID` - AWS access key for Docker build
- `DOCKER_AWS_SECRET_ACCESS_KEY` - AWS secret key for Docker build

### Kubernetes Configuration
- `K8S_AWS_ACCESS_KEY_ID` - AWS access key for Kubernetes
- `K8S_AWS_SECRET_ACCESS_KEY` - AWS secret key for Kubernetes
- `K8S_AWS_REGION` - AWS region for EKS cluster
- `K8S_CLUSTER_NAME` - EKS cluster name
- `K8S_BASIC_NAMESPACE_NAME` - Base namespace name for deployments

## Branch Strategy

- **main** - Main development branch, triggers version bumping
- **releases/*** - Release branches (e.g., releases/dev, releases/sit, releases/uat, releases/preprd, releases/prd)

## Environment Mapping

The pipeline automatically determines the deployment environment based on the release branch:

- `releases/dev`, `releases/sit`, `releases/uat` → Deploy to `{K8S_BASIC_NAMESPACE_NAME}-private` namespace
- `releases/preprd`, `releases/prd` → Deploy to `{K8S_BASIC_NAMESPACE_NAME}-public` namespace

## Key Differences from GitLab CI

1. **Secrets Management**: Uses GitHub Secrets instead of GitLab CI variables
2. **Job Dependencies**: Uses `needs` keyword instead of GitLab's implicit dependencies
3. **Conditional Execution**: Uses GitHub's `if` conditions instead of GitLab's `rules`
4. **Artifacts**: Uses job outputs instead of GitLab artifacts for passing data between jobs
5. **Docker Build**: Uses official Docker actions instead of manual docker commands
6. **AWS Integration**: Uses official AWS actions for better integration

## Usage

1. Push to `main` branch to trigger version bumping
2. Create a release branch (e.g., `releases/dev`) to trigger the full CI/CD pipeline
3. The pipeline will:
   - Prepare build environment
   - Build and push Docker image to ECR
   - Deploy to the appropriate Kubernetes namespace
   - Create a git tag for the release

## Testing

To test the pipeline:

1. Create a pull request to trigger the test job
2. Merge to main to trigger version bumping
3. Create a release branch to trigger the full deployment pipeline

## Troubleshooting

- Ensure all required secrets are configured in GitHub repository settings
- Check that AWS credentials have the necessary permissions for ECR and EKS
- Verify that the Kubernetes cluster and namespaces exist
- Make sure the Docker build context and Dockerfile are properly configured
