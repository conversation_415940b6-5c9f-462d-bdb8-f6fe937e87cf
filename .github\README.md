# GitHub Actions CI/CD Setup

This repository uses GitHub Actions for continuous integration and deployment of the Incutix Next.js BSL End User Web application.

## Workflow Overview

The CI/CD pipeline consists of the following stages:

1. **Bump** - Automatically bumps version in package.json on main branch
2. **Prepare** - Prepares build environment for release branches
3. **Build** - Builds and pushes Docker image to ECR
4. **Deploy** - Deploys to Kubernetes cluster
5. **Backup** - Creates git tags for releases

## Required GitHub Secrets

You need to configure the following secrets in your GitHub repository settings:

### VEXMETA AWS Configuration
- `VEXMETA_ECR_AWS_ACCOUNT_ID` - AWS account ID for ECR
- `VEXMETA_ECR_AWS_REGION` - AWS region for ECR (e.g., us-east-1)
- `VEXMETA_ECR_AWS_ACCESS_KEY_ID` - AWS access key for ECR operations
- `VEXMETA_ECR_AWS_SECRET_ACCESS_KEY` - AWS secret key for ECR operations
- `VEXMETA_K8S_AWS_ACCESS_KEY_ID` - AWS access key for Kubernetes operations
- `VEXMETA_K8S_AWS_SECRET_ACCESS_KEY` - AWS secret key for Kubernetes operations
- `VEXMETA_K8S_AWS_REGION` - AWS region for EKS cluster

### Git Configuration (Optional)
- `CI_EMAIL` - Email for git commits (<NAME_EMAIL>)
- `CI_USER` - Username for git commits (defaults to GitHub Actions)
- `CI_USER_ACCESS_TOKEN` - GitHub personal access token with repo permissions (for backup job)

## Project Configuration

The workflow is configured for the following project:
- **ECR Repository**: `incutix-nextjs-bsl-enduserweb-{environment}`
- **K8S Cluster**: `vex-runtime-server`
- **K8S Workload**: `bsl-enduserweb`
- **Base Namespace**: `incutix`

## Branch Strategy

- **main** - Main development branch, triggers automatic version bumping in package.json
- **releases/dev** - Development environment release branch
- **releases/uat** - User Acceptance Testing environment release branch
- **releases/preprd** - Pre-production environment release branch
- **releases/prd** - Production environment release branch

## Environment Mapping

The pipeline automatically determines the deployment environment and namespace based on the release branch:

- `releases/dev`, `releases/uat` → Deploy to `incutix-private` namespace
- `releases/preprd`, `releases/prd` → Deploy to `incutix-public` namespace

## Version Management

- **Version Source**: All version information is extracted from `package.json` only
- **Version Bumping**: Automatic semantic versioning based on commit messages:
  - Commits containing "add", "new", "migrate" → Major version bump
  - Commits containing "update" → Minor version bump
  - All other commits (fix, debug, patch, etc.) → Patch version bump

## Workflow Jobs

### 1. Bump Job (main branch only)
- **Trigger**: Push to `main` branch
- **Purpose**: Automatically bumps version in package.json based on commit message
- **Actions**:
  - Analyzes commit message for version bump type
  - Updates package.json with new version
  - Commits and pushes changes back to main

### 2. Prepare Job (release branches only)
- **Trigger**: Push to `releases/*` branches
- **Purpose**: Prepares build environment and variables
- **Matrix**: Runs for each environment (dev, uat, preprd, prd)
- **Outputs**: Build version, environment info, ECR paths

### 3. Build Job (release branches only)
- **Trigger**: After prepare job completes
- **Purpose**: Builds and pushes Docker image to ECR
- **Matrix**: Runs for each environment
- **Actions**:
  - Builds Docker image with environment-specific tags
  - Pushes to ECR repository: `incutix-nextjs-bsl-enduserweb-{environment}`

### 4. Deploy Job (release branches only)
- **Trigger**: After build job completes
- **Purpose**: Deploys to Kubernetes cluster
- **Matrix**: Runs for each environment and job (enduserweb)
- **Actions**:
  - Updates Kubernetes deployment with new image
  - Deploys to appropriate namespace based on environment

### 5. Backup Job (release branches only)
- **Trigger**: After deploy job completes
- **Purpose**: Creates git tags for release tracking
- **Matrix**: Runs for each environment
- **Actions**: Creates and pushes git tags with format `{environment}-v{version}`

## Usage

1. **Development Workflow**:
   - Push to `main` branch to trigger automatic version bumping
   - Version will be updated in package.json based on commit message

2. **Deployment Workflow**:
   - Push to release branches (`releases/dev`, `releases/uat`, `releases/preprd`, `releases/prd`)
   - Full CI/CD pipeline will execute: prepare → build → deploy → backup

3. **Environment-Specific Deployments**:
   - Each release branch targets a specific environment
   - ECR repositories and K8S deployments are environment-specific

## Troubleshooting

- **Secrets**: Ensure all VEXMETA_* secrets are configured in GitHub repository settings
- **AWS Permissions**: Verify AWS credentials have ECR and EKS permissions
- **Kubernetes**: Check that cluster `vex-runtime-server` and namespaces exist
- **Docker**: Ensure Dockerfile is present and properly configured
- **Node.js**: Verify package.json exists and has a valid version field
