name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - releases/uat
      - releases/prd
      - releases/preprd
      - 'refs/heads/releases/uat'
      - 'refs/heads/releases/preprd'
      - 'refs/heads/releases/prd'
  workflow_dispatch:

env:
  # Project Configuration
  ECR_DOMAIN: ${{secrets.VEXMETA_ECR_AWS_ACCOUNT_ID}}.dkr.ecr.${{secrets.VEXMETA_ECR_AWS_REGION}}.amazonaws.com
  ECR_REPOSITORY: incutix-nextjs-enduserweb-v2
  IMAGE_TAG: ${{ github.sha }}
  K8S_CLUSTER_NAME: vex-runtime-server
  K8S_BASIC_NAMESPACE_NAME: incutix
  K8S_WORKLOAD_NAME: bsl-enduserweb

jobs:
  # Bump version on main branch
  bump:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Get current version
        id: get-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $VERSION"
          echo "VERSION=${VERSION}" >> $GITHUB_ENV

      - name: Bump version
        id: bump-version
        run: |
          # Parse version components
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          MAJOR=$(echo $CURRENT_VERSION | cut -d. -f1)
          MINOR=$(echo $CURRENT_VERSION | cut -d. -f2)
          PATCH=$(echo $CURRENT_VERSION | cut -d. -f3)

          # Get the last commit message
          COMMIT_MSG=$(git log -1 --pretty=%B)
          echo "Commit message: $COMMIT_MSG"

          # Determine which version component to bump based on commit message
          if echo "$COMMIT_MSG" | grep -i -E "add|new|migrate" > /dev/null; then
            # Bump major version
            NEW_MAJOR=$((MAJOR + 1))
            NEW_MINOR=0
            NEW_PATCH=0
            VERSION_TYPE="major"
          elif echo "$COMMIT_MSG" | grep -i -E "update" > /dev/null; then
            # Bump minor version
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$((MINOR + 1))
            NEW_PATCH=0
            VERSION_TYPE="minor"
          else
            # Default: Bump patch version (for 'fix', 'debug', 'patch', or any other message)
            NEW_MAJOR=$MAJOR
            NEW_MINOR=$MINOR
            NEW_PATCH=$((PATCH + 1))
            VERSION_TYPE="patch"
          fi

          NEW_VERSION="$NEW_MAJOR.$NEW_MINOR.$NEW_PATCH"
          echo "Bumping $VERSION_TYPE version to: $NEW_VERSION"

          echo "New version: $NEW_VERSION"

          # Update package.json version
          npm version $NEW_VERSION --no-git-tag-version
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV

      - name: Commit & Push updated version
        run: |
          git add package.json
          git commit -m "Bump version to $NEW_VERSION [skip ci]"
          git push origin HEAD:${GITHUB_REF#refs/heads/}

  # Prepare build environment for release branches
  prepare:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment:
          - ${{ contains(github.ref, 'releases/dev') && 'dev' || contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/preprd') && 'preprd' || contains(github.ref, 'releases/prd') && 'prd' }}
    if: startsWith(github.ref, 'refs/heads/releases/')
    outputs:
      build-version: ${{ steps.prepare-vars.outputs.build-version }}
      environment-lowercase: ${{ steps.prepare-vars.outputs.environment-lowercase }}
      environment-uppercase: ${{ steps.prepare-vars.outputs.environment-uppercase }}
      ecr-full-path: ${{ steps.prepare-vars.outputs.ecr-full-path }}
      ecr-repository: ${{ steps.prepare-vars.outputs.ecr-repository }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'yarn'

      - name: Define ECR Repository Name
        run: |
          ECR_REPOSITORY="${{ env.ECR_REPOSITORY }}-${{ matrix.environment }}"
          echo "ECR_REPOSITORY=$ECR_REPOSITORY" >> $GITHUB_ENV

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_ECR_AWS_REGION }}

      - name: Prepare build variables
        id: prepare-vars
        run: |
          # Get version from package.json only
          BUILD_VERSION=$(node -p "require('./package.json').version")

          MULTISTAGE_ENVIRONMENT_LOWERCASE=${{ matrix.environment }}
          MULTISTAGE_ENVIRONMENT_UPPERCASE=$(echo $MULTISTAGE_ENVIRONMENT_LOWERCASE | tr "[:lower:]" "[:upper:]")
          ECR_REPOSITORY_WITH_ENV="${{ env.ECR_REPOSITORY }}-${MULTISTAGE_ENVIRONMENT_LOWERCASE}"
          ECR_FULL_PATH="${{ env.ECR_DOMAIN }}/${ECR_REPOSITORY_WITH_ENV}:v${BUILD_VERSION}"

          echo "build-version=$BUILD_VERSION" >> $GITHUB_OUTPUT
          echo "environment-lowercase=$MULTISTAGE_ENVIRONMENT_LOWERCASE" >> $GITHUB_OUTPUT
          echo "environment-uppercase=$MULTISTAGE_ENVIRONMENT_UPPERCASE" >> $GITHUB_OUTPUT
          echo "ecr-full-path=$ECR_FULL_PATH" >> $GITHUB_OUTPUT
          echo "ecr-repository=$ECR_REPOSITORY_WITH_ENV" >> $GITHUB_OUTPUT

          echo "BUILD_VERSION=$BUILD_VERSION"
          echo "MULTISTAGE_ENVIRONMENT_LOWERCASE=$MULTISTAGE_ENVIRONMENT_LOWERCASE"
          echo "MULTISTAGE_ENVIRONMENT_UPPERCASE=$MULTISTAGE_ENVIRONMENT_UPPERCASE"
          echo "ECR_REPOSITORY_WITH_ENV=$ECR_REPOSITORY_WITH_ENV"
          echo "ECR_FULL_PATH=$ECR_FULL_PATH"

  # Build and push Docker image
  build:
    runs-on: ubuntu-latest
    needs: prepare
    if: startsWith(github.ref, 'refs/heads/releases/')
    strategy:
      matrix:
        environment:
          - ${{ contains(github.ref, 'releases/dev') && 'dev' || contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/preprd') && 'preprd' || contains(github.ref, 'releases/prd') && 'prd' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_ECR_AWS_REGION }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_ECR_AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          push: true
          tags: ${{ needs.prepare.outputs.ecr-full-path }}
          build-args: |
            EASYLIVE_CODEARTIFACT_AWS_ACCESS_KEY_ID=${{ secrets.VEXMETA_ECR_AWS_ACCESS_KEY_ID }}
            EASYLIVE_CODEARTIFACT_AWS_SECRET_ACCESS_KEY=${{ secrets.VEXMETA_ECR_AWS_SECRET_ACCESS_KEY }}
            PROJECT_ENV=${{ needs.prepare.outputs.environment-uppercase }}

  # Deploy to Kubernetes
  deploy:
    runs-on: ubuntu-latest
    needs: [prepare, build]
    if: startsWith(github.ref, 'refs/heads/releases/')
    strategy:
      matrix:
        environment:
          - ${{ contains(github.ref, 'releases/dev') && 'dev' || contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/preprd') && 'preprd' || contains(github.ref, 'releases/prd') && 'prd' }}
        job: [enduserweb]
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.VEXMETA_K8S_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.VEXMETA_K8S_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.VEXMETA_K8S_AWS_REGION }}
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.22.13'
      
      - name: Setup eksctl
        run: |
          curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
          sudo mv /tmp/eksctl /usr/local/bin
      
      - name: Configure kubectl for EKS
        run: |
          aws eks update-kubeconfig --name ${{ env.K8S_CLUSTER_NAME }} --region ${{ secrets.VEXMETA_K8S_AWS_REGION }}
          eksctl utils write-kubeconfig --cluster=${{ env.K8S_CLUSTER_NAME }} --region=${{ secrets.VEXMETA_K8S_AWS_REGION }}

      - name: Deploy to Kubernetes
        run: |
          K8S_WORKLOAD_NAME="${{ matrix.job }}-${{ matrix.environment }}"

          if [[ "${{ matrix.environment }}" =~ ^(dev|uat)$ ]]; then
            K8S_NAMESPACE_NAME="${{ env.K8S_BASIC_NAMESPACE_NAME }}-private"
          fi
          if [[ "${{ matrix.environment }}" =~ ^(preprd|prd)$ ]]; then
            K8S_NAMESPACE_NAME="${{ env.K8S_BASIC_NAMESPACE_NAME }}-public"
          fi

          echo "Deploy Deployment [${K8S_WORKLOAD_NAME}] with Image [${{ needs.prepare.outputs.ecr-full-path }}] in [${K8S_NAMESPACE_NAME}]"
          kubectl set image deployment/${K8S_WORKLOAD_NAME} ${K8S_WORKLOAD_NAME}=${{ needs.prepare.outputs.ecr-full-path }} -n ${K8S_NAMESPACE_NAME}

  # Backup - create git tags
  backup:
    runs-on: ubuntu-latest
    needs: [prepare, deploy]
    if: startsWith(github.ref, 'refs/heads/releases/')
    strategy:
      matrix:
        environment:
          - ${{ contains(github.ref, 'releases/dev') && 'dev' || contains(github.ref, 'releases/uat') && 'uat' || contains(github.ref, 'releases/preprd') && 'preprd' || contains(github.ref, 'releases/prd') && 'prd' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'yarn'

      - name: Setup Yarn Berry
        run: yarn set version berry

      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Create and push git tag
        run: |
          GIT_TARGET_BRANCH=${GITHUB_REF#refs/heads/}
          GIT_TAG_PREFIX=${{ matrix.environment }}

          # Get version from package.json only
          BUILD_VERSION=$(node -p "require('./package.json').version")

          git checkout $GIT_TARGET_BRANCH
          yarn dlx standard-version -t "$GIT_TAG_PREFIX-v" --release-as $BUILD_VERSION --quiet --skip.changelog --skip.commit
          git push --follow-tags
