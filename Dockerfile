# Stage 1: Build the application
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install
RUN npm install next react react-dom
RUN npm install -g next
RUN npm install next-intl

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Serve the application
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy built application from the builder stage
COPY --from=builder /app ./

# Install production dependencies
RUN npm install --only=production

# Expose ports
EXPOSE 3000

# Start the application
CMD ["npm", "start"]




