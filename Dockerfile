# Stage 1: Build the application
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Define build arguments for environment variables
ARG NODE_ENV=production
ARG NEXT_PUBLIC_API_BASE
ARG NEXTAUTH_URL
ARG WEBSOCKET_URL
ARG NEXTAUTH_SECRET
ARG GOOGLE_OAUTH_CLIENT_ID
ARG GOOGLE_OAUTH_CLIENT_SECRET
ARG NEXT_PUBLIC_INCUTIX_URL
ARG NEXT_PUBLIC_APPLICATION_KEY
ARG NEXT_PUBLIC_EMPTY_TOKEN
ARG NEXT_PUBLIC_URL_PREFIX
ARG PROJECT_ENV

# Set environment variables for build time
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_API_BASE=$NEXT_PUBLIC_API_BASE
ENV NEXTAUTH_URL=$NEXTAUTH_URL
ENV WEBSOCKET_URL=$WEBSOCKET_URL
ENV NEXTAUTH_SECRET=$NEXTAUTH_SECRET
ENV GOOGLE_OAUTH_CLIENT_ID=$GOOGLE_OAUTH_CLIENT_ID
ENV GOOGLE_OAUTH_CLIENT_SECRET=$GOOGLE_OAUTH_CLIENT_SECRET
ENV NEXT_PUBLIC_INCUTIX_URL=$NEXT_PUBLIC_INCUTIX_URL
ENV NEXT_PUBLIC_APPLICATION_KEY=$NEXT_PUBLIC_APPLICATION_KEY
ENV NEXT_PUBLIC_EMPTY_TOKEN=$NEXT_PUBLIC_EMPTY_TOKEN
ENV NEXT_PUBLIC_URL_PREFIX=$NEXT_PUBLIC_URL_PREFIX
ENV PROJECT_ENV=$PROJECT_ENV

# Install dependencies
COPY package*.json ./
RUN npm install
RUN npm install next react react-dom
RUN npm install -g next
RUN npm install next-intl

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Serve the application
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Define runtime arguments (same as build args)
ARG NODE_ENV=production
ARG NEXT_PUBLIC_API_BASE
ARG NEXTAUTH_URL
ARG WEBSOCKET_URL
ARG NEXTAUTH_SECRET
ARG GOOGLE_OAUTH_CLIENT_ID
ARG GOOGLE_OAUTH_CLIENT_SECRET
ARG NEXT_PUBLIC_INCUTIX_URL
ARG NEXT_PUBLIC_APPLICATION_KEY
ARG NEXT_PUBLIC_EMPTY_TOKEN
ARG NEXT_PUBLIC_URL_PREFIX
ARG PROJECT_ENV

# Set runtime environment variables
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_API_BASE=$NEXT_PUBLIC_API_BASE
ENV NEXTAUTH_URL=$NEXTAUTH_URL
ENV WEBSOCKET_URL=$WEBSOCKET_URL
ENV NEXTAUTH_SECRET=$NEXTAUTH_SECRET
ENV GOOGLE_OAUTH_CLIENT_ID=$GOOGLE_OAUTH_CLIENT_ID
ENV GOOGLE_OAUTH_CLIENT_SECRET=$GOOGLE_OAUTH_CLIENT_SECRET
ENV NEXT_PUBLIC_INCUTIX_URL=$NEXT_PUBLIC_INCUTIX_URL
ENV NEXT_PUBLIC_APPLICATION_KEY=$NEXT_PUBLIC_APPLICATION_KEY
ENV NEXT_PUBLIC_EMPTY_TOKEN=$NEXT_PUBLIC_EMPTY_TOKEN
ENV NEXT_PUBLIC_URL_PREFIX=$NEXT_PUBLIC_URL_PREFIX
ENV PROJECT_ENV=$PROJECT_ENV

# Copy built application from the builder stage
COPY --from=builder /app ./

# Install production dependencies
RUN npm install --only=production

# Expose ports
EXPOSE 3000

# Start the application
CMD ["npm", "start"]




