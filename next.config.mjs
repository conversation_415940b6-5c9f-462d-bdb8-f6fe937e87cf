import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  async redirects() {
    return [
      {
        source: "/",
        destination: "/all-products",
        permanent: true,
      },
    ];
  },
  env: {
    WEBSOCKET_URL: process.env.WEBSOCKET_URL
  },
  images: {
    domains: ['s3.ap-southeast-1.amazonaws.com']
  }
};

export default withNextIntl(nextConfig);
