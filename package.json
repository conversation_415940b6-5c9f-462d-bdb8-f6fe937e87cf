{"name": "funverse-managment-system", "version": "0.1.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@emotion/cache": "^11.11.0", "@hookform/resolvers": "^3.6.0", "@mui/icons-material": "^5.15.19", "@mui/material-nextjs": "^5.15.11", "@mui/x-date-pickers": "^7.7.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.40.1", "@tanstack/react-table": "^8.17.3", "countries-and-timezones": "^3.8.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "i18next": "^24.2.1", "jwt-decode": "^4.0.0", "lodash.debounce": "^4.0.8", "mui-one-time-password-input": "^3.0.2", "next": "^14.2.29", "next-auth": "^4.24.7", "next-i18next": "^15.4.1", "next-intl": "^3.26.5", "next-redux-wrapper": "^8.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.5", "react-i18next": "^15.4.0", "react-international-phone": "^4.3.0", "react-qr-code": "^2.0.15", "react-redux": "^9.1.2", "react-toastify": "^10.0.5", "redux": "^5.0.1", "xior": "^0.5.1", "zod": "^3.23.8"}, "devDependencies": {"@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "typescript": "^5"}}