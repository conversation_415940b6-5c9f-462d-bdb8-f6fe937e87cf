'use client'
import * as React from 'react';
import { styled, Theme, useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import { Typography , useMediaQuery, CircularProgress, ImageList, ImageListItem, Snackbar, Modal} from '@mui/material';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import Button from '@mui/material/Button';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import Inventory2OutlinedIcon from '@mui/icons-material/Inventory2Outlined';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import {IincutixProductDetails} from '@/interface/IincutixProductDetails';
import {IRelatedProducts} from "@/interface/IRelatedProducts";
import { useLocale, useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import ProductList from '../../../product-list/productList';
import ProductAttributes from '../product-attributes/productAttributes';
import ShoppingCart from '@/components/shopping-cart/ShoppingCart';
import {apiClient } from "@/utils/apiClient";
import CustomSnackbar from '@/components/Snackbar';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import { useDispatch,useSelector  } from "react-redux";
import { fetchCartCount } from "@/redux/cartCountSlice";
import { AppDispatch, RootState } from '@/redux/store';
import{ fetchUserData } from "@/redux/userSlice";
import LoadingBackdrop from '@/components/LoadingBackdrop';
import {getTimezonesForCountry} from "countries-and-timezones"
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

interface Props {
    params: {
        token: string;
        id: string;
    };
}

const ProductImage = styled('img')(({ theme }) => ({
    width: '100%',
    height: 'auto',
    borderRadius: theme.shape.borderRadius,
    objectFit: 'cover',
    [theme.breakpoints.up('md')]: {
        maxHeight: '500px',
    },
}));

const StickyButtonContainer = styled('div')(({ theme }) => ({
  position: 'fixed',
  bottom: 0,
  left: 0,
  right: 0,
  display: 'flex',
  justifyContent: 'center',
  padding: theme.spacing(2),
  backgroundColor: 'white',
  boxShadow: '0px -2px 10px rgba(0, 0, 0, 0.1)',
  zIndex: 1000,
  [theme.breakpoints.up('md')]: {
    display: 'none', 
  },
}));

const whiteButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'black',
  backgroundColor: 'transparent',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(230, 232, 236, 0.1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
  display: 'flex', 
  justifyContent: 'space-between', 
  alignItems: 'center', 
  width: 'auto', 
  minWidth: '140px', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }
}

const greenButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'rgba(230, 232, 236, 1)',
  backgroundColor: 'rgba(91, 170, 100, 1)',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(91, 170, 100, 1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
  display: 'flex', 
  justifyContent: 'space-between', 
  alignItems: 'center', 
  width: 'auto', 
  minWidth: '140px', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }  
}

interface ImageGalleryProps {
  images: {
    id: string | number;
    thumbnail: string;
    fullSize: string;
    alt?: string;
  }[];
  // initialSelectedIndex?: number;
}

const StyledMainImage = styled('img')({
  width: '100%',
  height: 'auto',
  objectFit: 'cover',
  borderRadius: 4,
  cursor: 'pointer',
  radius: "8px"
});

const StyledThumbnail = styled('img')<{ selected: boolean }>(({ theme, selected }) => ({
    width: '60px',
    height: '60px',
    objectFit: 'contain',
    borderRadius: 4,
    cursor: 'pointer',
    opacity: selected ? 1 : 0.7,
    border: selected ? `2px solid rgba(214, 246, 211, 1)` : '2px solid transparent',
    transition: 'opacity 0.3s, border 0.3s',
    '&:hover': {
        opacity: 1,
    },
    backgroundColor: "#f5f5f5",
    padding: "8px",
    [theme.breakpoints.up('sm')]: {
        width: '80px',
        height: '80px',
    },
    [theme.breakpoints.up('md')]: {
        width: '100px',
        height: '100px',
    }
}));

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  // border: '2px solid #000',
  boxShadow: 24,
  p: 4,
  paddingTop:"5px",
  paddingRight:"20px",
  borderRadius:"24px"
  // backgroundColor:"rgba(0, 0, 0, 0.5)"
};

const ProductDetails = ({ params }: Props) => {
    const { token, id } = params;
    const [quantity, setQuantity] = useState<number>(1);
    const [showAllProducts, setShowAllProducts] = useState(false);
    const [productDetails,setProductDetails] = useState<IincutixProductDetails | undefined>(undefined);
    const [relatedProdcuts, setRelatedProdcuts] = useState<IRelatedProducts | undefined>(undefined);
    const currentLocale = useLocale();
    const localeToLanguageCode: Record<string, string> = {
      en: 'EN',
      zh: 'TC'
    };
    const languageCode = localeToLanguageCode[currentLocale] || 'EN';
    const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
    const t = useTranslations("product_details");
    const theme = useTheme();
    const [selectedIndex, setSelectedIndex] = useState<number>(0);
    const [isAvailable,setIsAvailable] = useState<boolean>(true);
    const [cartOpen, setCartOpen] = useState<boolean>(false);
    const [open, setOpen] = useState(false);
    const [skuId,setSkuId] = useState<number>(0);
    const [openSnackBar, setOpenSnackBar] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const handleModalOpen = () => setOpenModal(true);
    const handleModalClose = () => setOpenModal(false);
    const sc = useTranslations("shopping_cart_component");
    const dispatch = useDispatch<AppDispatch>();
    const {userData, loading, error} = useSelector((state: RootState)=> state.userData);
    const [isConstruction,setConstruction] =useState<boolean>(false);
    const [idempotencyKeyDto,setIdempotencyKeyDto] = useState<any>(null);
    const [loadingBackdropOpen,setLoadingBackdropOpen] = useState<boolean>(false);

    // const { getTimezonesForCountry } = require('countries-and-timezones');
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const thaiTimezones = getTimezonesForCountry(productDetails?.data?.country?.code as string);
    const timezoneName = thaiTimezones?.[0]?.name || dayjs.tz.guess() || "UTC";
    const formatDateWithTimezone = (timestamp?: number) => {
      if (!timestamp) return "-";
      return dayjs.unix(timestamp).tz(timezoneName).format("YYYY-MM-DD HH:mm");
    };

    const saleStartDate = productDetails?.data?.saleStartDate;
    const salesEndDate = productDetails?.data?.salesEndDate;
    const pickupStartDate = productDetails?.data?.shipping?.pickupStartDate;
    const pickupEndDate = productDetails?.data?.shipping?.pickupEndDate;

    const formatSaleStartDate = formatDateWithTimezone(saleStartDate);
    const formatSalesEndDate = formatDateWithTimezone(salesEndDate);
    const formatPickupStartDate = formatDateWithTimezone(pickupStartDate);
    const formatPickupEndDate = formatDateWithTimezone(pickupEndDate);

    // const formatSaleStartDate = saleStartDate ?
    //     format(new Date(saleStartDate * 1000), "yyyy-MM-dd HH:mm") : 
    //     "-";
     
    // const formatSalesEndDate = salesEndDate ?
    //     format(new Date(salesEndDate * 1000), "yyyy-MM-dd HH:mm") : 
    //     "-";

    // const formatPickupStartDate = pickupStartDate ?
    //     format(new Date(pickupStartDate * 1000), "yyyy-MM-dd HH:mm") : 
    //     "-";  

    // const formatPickupEndDate = pickupEndDate ?
    //     format(new Date(pickupEndDate * 1000), "yyyy-MM-dd HH:mm") : 
    //     "-";   

    const getParsedUserToken = () => {
      const userIdToken = localStorage.getItem('userIdToken');
      
      if (!userIdToken) {
        console.log('No user token found');
        return null;
      }
      
      try {
        return JSON.parse(userIdToken);
      } catch (error) {
        console.error('Error parsing user token:', error);
        return null;
      }
    };
    // console.log("cehck currentLocale",currentLocale)
    const postGetIdempotencyKey = async () =>{

        const parsedToken = getParsedUserToken();

        if (!parsedToken) {
            console.log('No user token found');
            return;
        }

        try{

          const productDto = {
            cartType:"productOnly",
            productId:productDetails?.data?.productId,
            skuId:skuId,
            quantity:quantity
          }

          const data = await apiClient.post(`/shopping-cart/checkout/only-one/idempotency-key`,parsedToken?.token,productDto);

          if(data.success){
            setIdempotencyKeyDto(data);
            console.log('post idempotency key success',data);
            return data.data?.token; 
          }else if(!data.success){
            console.log("post failed",data)
            setOpen(true);
          }

        }catch(error){

          console.error('post idempotency key error:', error);
          setOpen(true);
          throw error;

        }

    }

    const handleInstantCheckout = async(token:string) =>{

        const parsedToken = getParsedUserToken();

        if (!parsedToken) {
            console.log('No user token found');
            return;
        }

        try{

          const productDto = {
            cartType:"productOnly",
            productId:productDetails?.data?.productId,
            skuId:skuId,
            quantity:quantity
          }

          const data = await apiClient.post(
            `/shopping-cart/checkout/only-one`,
            parsedToken?.token,
            productDto,
            {"X-Incutix-Idempotency-Key":token}
          )

          if(data.success){

            console.log("Post shopping checkout success",data.data?.orderId);

            window.location.href = `/order/${data.data?.orderId}`;

          }else if(!data.success){

            console.log("Post shopping checkout failed",data)

          }

        }catch(error){

          console.error('post idempotency key error:', error);
          throw error;

        }
    }

    const handleConstructionClose = () => {
        setConstruction(false);
    }

    const handleSnackBarClose = () =>{
        setOpenSnackBar(false);
    };

    const handleClick = () => {
      setOpen(true);
      { userData && handleCartOpen()}
    };

    const handlePaymentButton = async () =>{
      // setOpen(true)
      // setConstruction(true);
      try{
       const token = await postGetIdempotencyKey();
       if (!token) throw new Error("Failed to get token");
       setLoadingBackdropOpen(true)
       await handleInstantCheckout(token);
      }catch(error){
        console.error("Checkout failed:", error);
      }
    }

    const handleClose = () => {
      // if (reason === 'clickaway') {
      //   return;
      // }
      setOpen(false); 
    };

    const handleCartQuantity = (quantity:number) =>{
      setQuantity(quantity);
    }

    const handleSkuIdNumber = (skuId:number) =>{
      setSkuId(skuId)
    }

    const handleCartOpen = () => {
      setCartOpen(true);
      setOpenModal(false);
    };
    const handleCartClose = () => setCartOpen(false);

    const handleAttributesAvailable = (isAvailable:boolean) => {
      setIsAvailable(isAvailable);
    }

    const handleThumbnailClick = (index: number) => {
      setSelectedIndex(index);
    };

    const localeToLanguageCodes: Record<string, string> = {
        en: 'en-US',
        zh: 'zh-HK'
    };

    const languageCodes = localeToLanguageCodes[currentLocale] || 'en-US';

    const handleLoginRedirectUrl = () =>{
        window.location.href = `${process.env.NEXT_PUBLIC_INCUTIX_URL}/${languageCodes}/login`;
    };
    // if (!images || images.length === 0) {
    //   return <Box>No images available</Box>;
    // }
    console.log("userData",userData)
    console.log("productDetails",productDetails)
      

    const fetchProductDetails = async () =>{
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/api/public/v1/products/${params.id}?language=${languageCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }, 
      });
      const data = await response.json();
      setProductDetails(data);
    }

    const fetchRelatedProdcuts = async () =>{
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/api/public/v1/products/related-products/${params.id}?language=${languageCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }, 
      });
      const data = await response.json();
      setRelatedProdcuts(data);
    }
    console.log("relatedProdcuts",relatedProdcuts)
    const addCartItemApi = async () => {
        try {
            const userIdToken = localStorage.getItem('userIdToken');
            
            if (!userIdToken) {
                console.log('No user token found');
                setOpen(true);
                return;
            }

            if(userData === null){
               setOpen(true);
              return;
            }

            const parsedToken = JSON.parse(userIdToken);  

            const addCartItemDto = {
                cartItemType: "product",
                productId: productDetails?.data?.productId,
                skuId: skuId,
                quantity: quantity
            }

            const data = await apiClient.post(`/shopping-cart`, parsedToken?.token, addCartItemDto);

            if(!data.success){
              console.log("Error",data.error.message)
              if(data?.error?.code.includes("CART_7005")){
                  handleModalOpen();
              }
              console.log("Error shopping cart button data",data)
            }else if(data.success){
              console.log("success add to cart api call")
              handleClick();
              setOpenSnackBar(true);
              dispatch(fetchCartCount());
            }

        } catch (error) {
            console.error('Error adding item to cart:', error);

            throw error; 
        }
    }

    useEffect(() => {
        fetchProductDetails();
        fetchRelatedProdcuts();
    }, []);

    useEffect(()=>{
        dispatch(fetchUserData())
    },[])

    const titleStyle = {
        color:"rgba(91, 170, 100, 1)",
        mb:2
    }
    
    const textDecoration = {
        borderLeft: "5px solid rgba(91, 170, 100, 1)",
        borderRadius: "4px",
        paddingLeft: "10px",
        mt:5
    }

  
    const renderShoppingCartButton = () =>{
      if(isMobile){
        return(
          <>
            <StickyButtonContainer>
              <Button              
                sx={whiteButtonCss} disabled={!isAvailable} onClick={addCartItemApi}>
                {t('addToCart')}
              </Button>
                <ShoppingCart open={cartOpen} width={"100%"} onClose={handleCartClose}/>
              {/* <Snackbar
                open={open && !userData}
                autoHideDuration={3000}
                onClose={handleClose}
                message="please login"
                anchorOrigin={{
                  vertical: 'top',  
                  horizontal: 'center'
                }}
                sx={{
                  '& .MuiSnackbar-root': {
                    position: 'fixed',
                    top: '20px !important', 
                  },
                  zIndex: 9999
                }}
              /> */}
             {/* <CustomSnackbar
                  open={open && !userData}
                  onClose={handleClose}
                  autoHideDuration={2000}
                  message="請登入"
                  color='rgba(245, 34, 45, 1)'
                  backgroundColor='rgba(255, 245, 245, 1)'
              /> */}
            {
              isAvailable ?<Button
              sx={greenButtonCss}
              onClick={handlePaymentButton}
            >
                {t('buyNow')}
            </Button> : <Button sx={greenButtonCss}>
                {t('notYetOnSale')}
            </Button>
            
            }
            </StickyButtonContainer>
          </>
        )
      }else if(!isMobile){
        return(
          <>
            <Button              
              sx={whiteButtonCss} disabled={!isAvailable} onClick={addCartItemApi}>
                {t('addToCart')}
            </Button>
            <ShoppingCart open={cartOpen} width={350} onClose={handleCartClose}/>
             {/* <Snackbar
                open={open && !userData}
                autoHideDuration={3000}
                onClose={handleClose}
                message="please login"
              /> */}
              {/* <CustomSnackbar
                open={open && !userData}
                onClose={handleClose}
                autoHideDuration={2000}
                message="請登入"
                color='rgba(245, 34, 45, 1)'
                backgroundColor='rgba(255, 245, 245, 1)'
              /> */}
            {
              isAvailable ?             <Button
              sx={greenButtonCss}
              onClick={handlePaymentButton}
            >
                {t('buyNow')}
            </Button> : <Button sx={{
              ...greenButtonCss,
              // color: 'white',
              '&.Mui-disabled': { 
              color: 'white', 
              opacity: 0.7, 
              }
              }} disabled>
                {t('notYetOnSale')}
            </Button>
            }
          </>
        )
      }
    }

    const renderImageGallery = () => {

        const allImages = productDetails?.data?.productSKU?.flatMap(
          (product) => product.media?.image || []
        ) || [];


        if (allImages.length === 0) {
          return   <Grid container justifyContent="center" alignItems="center" 
                  sx={{
                    height:"50vh",
                    marginRight:{md:"400px"}
                  }}
                  >
                    <CircularProgress sx={{color:"#ff7802"}}/>
                  </Grid>;
        }


        const safeSelectedIndex = Math.min(selectedIndex, allImages.length - 1);

        return (
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              gap: 2,
              width: '100%',
            }}
          >
            {/* 主圖 */}
            <Box
              sx={{
                flex: 3,
                height: { xs: '300px', md: '500px' },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: theme.palette.grey[100],
                borderRadius: 1,
                overflow: 'hidden',
              }}
            >
              <StyledMainImage
                src={allImages[safeSelectedIndex]?.path}
                alt={`Main image ${safeSelectedIndex + 1}`}
              />
            </Box>

            {/* 縮略圖列表 */}
            <Box
              sx={{
                flex: 1,
                maxHeight: { xs: '150px', md: '500px' },
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: theme.palette.grey[400],
                  borderRadius: '3px',
                },
                // display:"flex",
                // justifyContent:"center"
              }}
            >
              <ImageList cols={isMobile ? 4 : 1} gap={8}>
                {allImages.map((image, index) => (
                  <ImageListItem key={image.priority || index}>
                    <StyledThumbnail
                      src={image.path}
                      selected={index === safeSelectedIndex}
                      onClick={() => handleThumbnailClick(index)}
                    />
                  </ImageListItem>
                ))}
              </ImageList>
            </Box>
          </Box>
        );
      };

    return (
      <>
        <Box sx={{ flexGrow: 1, p: { xs: 2, md: 4 } }}>
            <Grid container spacing={4}>
                {/* Image Column - appears first on mobile */}
                <Grid item xs={12} md={6}>
                    {/* <ProductImage 
                        src={productDetails?.data?.media?.image?.[0]?.path} 
                    /> */}
                    {renderImageGallery()}
                </Grid>

                {/* Description Column */}
                <Grid item xs={12} md={6}>
                    <Stack spacing={3}>
                        <Typography  component="h1" sx={{ color:"rgba(119, 126, 144, 1)",fontSize:"16px"}}>
                            {productDetails?.data?.event?.name}
                        </Typography>

                        <Typography component="h1" sx={{ color:"black",fontSize:"32px"}}>
                           {productDetails?.data?.productName}
                        </Typography>
                        
                        <Typography variant="body1" paragraph sx={{ color:"rgba(35, 38, 47, 1)",fontSize:"16px"}}>
                           {/* {productDetails?.data?.description} */}
                            {productDetails?.data?.description && (
                              <Typography 
                                variant="body2" 
                                component="div"
                                dangerouslySetInnerHTML={{ 
                                  __html: productDetails.data.description
                                }} 
                              />
                            )}
                        </Typography>
                        
                        <Box sx={{
                          backgroundColor:"rgba(240, 251, 239, 1)",
                          padding:3,
                          borderRadius:"16px"
                          }}>
                          <Typography variant="body1" sx={{
                              color:"rgba(91, 170, 100, 1)",
                              fontSize:"16px"
                            }}
                            >{t('saleTime')} (GMT{thaiTimezones?.[0].utcOffsetStr}):</Typography>
                          <Typography variant="body1" sx={{
                              color:"rgba(91, 170, 100, 1)",
                              fontSize:"16px"
                            }}
                            >{formatSaleStartDate} - {formatSalesEndDate} </Typography>
                        </Box>
                        <Box>
                            <Typography variant="h6" gutterBottom>
                            </Typography>
                            {
                              productDetails ? 
                              <ProductAttributes 
                              productDetails={productDetails}
                              handleAttributesAvailable={handleAttributesAvailable}
                              handleCartQuantity={handleCartQuantity}
                              handleSkuIdNumber={handleSkuIdNumber}
                              />
                              :                   
                              <Grid container justifyContent="center" alignItems="center" 
                                sx={{
                                  height:"50vh"
                                }}
                                >
                                  <CircularProgress sx={{color:"#ff7802"}}/>
                              </Grid>
                            }
                        </Box>
                        
                        <Box sx={{
                           display:"flex",
                          justifyContent:"center",
                          gap:6
                        }}>
                          {renderShoppingCartButton()}
                        </Box>
                        {/* Add to cart button or other actions would go here */}
                    </Stack>
                </Grid>
            </Grid>
            <Box sx={textDecoration}>
              <Typography variant="h2" sx={titleStyle}>{t('ticketInfo')}</Typography>
            </Box>
            <Box sx={{display:"flex",gap:1,mb:1}}>
              <CalendarTodayOutlinedIcon sx={{color:"rgba(91, 170, 100, 1)"}}/><Typography variant="body2">{t('pickupTime')} ： {formatPickupStartDate} - {formatPickupEndDate}</Typography>
            </Box>
            {/* <Box sx={{display:"flex",gap:1,mb:1}}>
              <Inventory2OutlinedIcon sx={{color:"rgba(91, 170, 100, 1)"}}/><Typography variant="body2">送貨方式：{productDetails?.data?.shipping?.shippingMethod.map(method => <p key={method}>{method}</p>)}</Typography>
            </Box>  */}
            <Box sx={{ display: "flex", gap: 1, mb: 1, alignItems: "center" }}>
              <Inventory2OutlinedIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
              <Typography variant="body2" sx={{ display: "flex", gap: 1, alignItems: "center" }}>
               {t('deliveryMethod')} :
            {(() => {
              const methods = productDetails?.data?.shipping?.shippingMethod || [];
              const hasExpress = methods.some(value => value.includes("express"));
              const hasStorePickup = methods.some(value => value.includes("storePickup"));

              // 組合多個結果
              let result = [];
              if (hasExpress) result.push(`${t("express")}`);
              if (hasStorePickup) result.push(`${t("storePickup")}`);
              
              return result.length > 0 ? result.join(", ") : "其他運輸方式";
            })()}
              </Typography>
            </Box>
            <Box sx={{display:"flex",gap:1,mb:1}}>
              <LocationOnOutlinedIcon sx={{color:"rgba(91, 170, 100, 1)"}}/><Typography variant="body2">{t('pickupLocation')}： {productDetails?.data?.shipping?.pickupVenue}</Typography>
            </Box>  
            <Box sx={textDecoration}>
              <Typography variant="h2" sx={titleStyle}>{t('productTheme')}</Typography>
            </Box>
            {productDetails?.data?.productIntroduction && (
              <Typography 
                variant="body2" 
                component="div"
                dangerouslySetInnerHTML={{ 
                  __html: productDetails.data.productIntroduction 
                }} 
              />
            )}
            <Box sx={textDecoration}>
              <Typography variant="h2" sx={titleStyle}>{t('relatedProducts')}</Typography>
            </Box>
            <Grid item xs={12} md={8}>
              <Grid container spacing={2}>
                    {
                      relatedProdcuts?.data?.items && relatedProdcuts?.data?.items
                      .slice(0, showAllProducts ? relatedProdcuts.data.items.length : 2)
                      .filter((value)=>(
                        value.productId !== productDetails?.data?.productId
                      ))
                      .map((value)=>(
                        <Grid item xs={12} sm={6} md={4} key={value.productId}>
                          <ProductList params={params} key={value.productId} items={value}/>
                        </Grid>
                      ))
                    }
              </Grid>
            </Grid>
              {relatedProdcuts?.data?.items && relatedProdcuts.data.items.length > 2 && (
                <Button 
                  onClick={() => setShowAllProducts(!showAllProducts)}
                  sx={{
                    borderColor: 'rgba(230, 232, 236, 1)',
                    color: 'rgba(230, 232, 236, 1)',
                    backgroundColor: 'rgba(91, 170, 100, 1)',
                    mt:2,
                    '&:hover': {
                      borderColor: 'rgba(230, 232, 236, 0.8)',
                      backgroundColor: 'rgba(91, 170, 100, 1)',
                    },
                    '&:focus': {
                      borderColor: 'rgba(230, 232, 236, 1)',
                    },
                    borderRadius: '30px',
                    padding: '8px 40px', 
                    fontSize: '14px',
                    textTransform: 'none',
                    borderWidth: '1px',
                    borderStyle: 'solid',
                    display: 'flex', 
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    width: 'auto', 
                    minWidth: '140px', 
                    '& .MuiSvgIcon-root': {
                      color: 'black',
                      marginLeft: '8px', 
                      fontSize: '18px',
                    }
                  }}
                >
                  {showAllProducts ? t('collapse') : t('viewAll')}
                </Button>
              )}
             <CustomSnackbar
                  open={openSnackBar}
                  onClose={handleSnackBarClose}
                  autoHideDuration={2000}
                  message={sc('products_add')}
                  color='rgba(91, 170, 100, 1)'
                  backgroundColor='rgba(240, 251, 239, 1)'
              />
              <Modal
                  open={openModal}
                  onClose={handleModalClose}
                  aria-labelledby="modal-modal-title"
                  aria-describedby="modal-modal-description"
                  sx={{backgroundColor:"rgba(0, 0, 0, 0.5)"}}
                > 
                      <Box sx={modalStyle}>
                        <Box sx={{display:"flex",justifyContent:"flex-end"}}>
                          <IconButton onClick={handleModalClose}>
                          <CancelOutlinedIcon/>
                          </IconButton>
                        </Box>
                        <Typography id="modal-modal-title" variant="h2" component="h2">
                          {sc('notice')}！
                        </Typography>
                        <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                          {t('same_region_notice')}
                        </Typography>
                        <Box sx={{display:"flex",justifyContent:"space-around",mt:2}}>
                          <Button sx={whiteButtonCss} onClick={handleModalClose}>{sc('back')}</Button><Button onClick={handleCartOpen} sx={greenButtonCss}>{sc('checkout_now')}</Button>
                        </Box>
                      </Box>
              </Modal>  
               <Modal
                  open={open && !userData}
                  onClose={handleClose}
                  aria-labelledby="modal-modal-title"
                  aria-describedby="modal-modal-description"
                  sx={{backgroundColor:"rgba(0, 0, 0, 0.5)"}}
                > 
                      <Box sx={modalStyle}>
                        <Box sx={{display:"flex",justifyContent:"flex-end"}}>
                          <IconButton onClick={handleClose}>
                          <CancelOutlinedIcon/>
                          </IconButton>
                        </Box>
                        <Box sx={{display:"flex",justifyContent:"center"}}>
                        <Typography id="modal-modal-title" variant="h2" component="h2">
                          {sc('please_login')}
                        </Typography>
                        </Box>
                        <Box sx={{display:"flex",justifyContent:"space-around",mt:2}}>
                          <Button sx={whiteButtonCss} onClick={handleClose}>{sc('back')}</Button><Button sx={greenButtonCss} onClick={handleLoginRedirectUrl}>{sc('login_now')}</Button>
                        </Box>
                      </Box>
              </Modal>
              <Modal
                  open={isConstruction}
                  onClose={handleConstructionClose}
                  aria-labelledby="modal-modal-title"
                  aria-describedby="modal-modal-description"
                  sx={{backgroundColor:"rgba(0, 0, 0, 0.5)"}}
                > 
                      <Box sx={modalStyle}>
                        <Box sx={{display:"flex",justifyContent:"flex-end"}}>
                          <IconButton onClick={handleConstructionClose}>
                          <CancelOutlinedIcon/>
                          </IconButton>
                        </Box>
                        <Box sx={{display:"flex",justifyContent:"center"}}>
                        <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                          Under Construction
                        </Typography>
                        </Box>
                        <Box sx={{display:"flex",justifyContent:"space-around",mt:2}}>
                          <Button sx={whiteButtonCss} onClick={handleConstructionClose}>{sc('back')}</Button>
                        </Box>
                      </Box>
              </Modal>  
        </Box>
        <LoadingBackdrop open={loadingBackdropOpen}/>
        </>
    );
}

export default ProductDetails;