import React, { useEffect, useState } from 'react';
import { Chip, Box, Typography, IconButton, Stack } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import { IincutixProductDetails } from '@/interface/IincutixProductDetails';

interface Props {
    productDetails: IincutixProductDetails;
    handleAttributesAvailable:(isAvailable:boolean)=> void;
    handleCartQuantity:(quantity:number)=> void;
    handleSkuIdNumber:(skuId:number)=> void;
}

function ProductAttributes({ productDetails,handleAttributesAvailable,handleCartQuantity,handleSkuIdNumber}: Props) {
    const [quantity, setQuantity] = useState(1);
    const [selectedAttributes, setSelectedAttributes] = useState<Record<string, string>>({});
    const [selectedSkuId, setSelectedSkuId] = useState<string | null>(null);
    const [attributeCategories, setAttributeCategories] = useState<string[]>([]);

    const iconButtonStyle = {
        border: "2px solid rgba(244, 245, 246, 1)",
        width: "30px",
        height: "30px",
        borderRadius: "999px",
        color: "rgba(255, 255, 255, 1)"
    }
    const SkuIdNumber = Number(selectedSkuId);
    const getCurrency = productDetails?.data?.currency;
    const matchedSKU = productDetails?.data?.productSKU?.find(value => 
    value.skuId === SkuIdNumber
    );

    if (matchedSKU) {
        handleAttributesAvailable(matchedSKU.isAvailable);
        } else {
        handleAttributesAvailable(false); // or whatever default you want
    }

    if(quantity){
        handleCartQuantity(quantity);
    }

    if(SkuIdNumber){
        handleSkuIdNumber(SkuIdNumber);
    }
    

    useEffect(() => {
        if (!productDetails?.data?.productAttributes) return;

        const { attributes, default: defaultAttrs = [] } = productDetails.data.productAttributes;
        
        const categories = attributes.map(attr => attr.category);
        setAttributeCategories(categories);

        const defaultSelected: Record<string, string> = {};
        defaultAttrs?.forEach(attr => {

            defaultSelected[attr.value] = attr.category;
        });
        setSelectedAttributes(defaultSelected);
    }, [productDetails]);

    useEffect(() => {
        if (!productDetails?.data?.productAttributes?.skuAttributes) return;

        const allSelected = attributeCategories.every(category => 
            selectedAttributes[category] !== undefined
        );

        if (allSelected) {

            for (const [skuId, attributes] of Object.entries(productDetails.data.productAttributes.skuAttributes)) {
                const isMatch = attributeCategories.every(category => {
                    const attr = attributes.find(attr => attr.category === category);
                    return attr?.value === selectedAttributes[category];
                });

                if (isMatch) {
                    setSelectedSkuId(skuId);
                    return;
                }
            }
            setSelectedSkuId(null); 
        }
    }, [selectedAttributes, productDetails, attributeCategories]);

    const handleAttributeClick = (category: string, value: string) => {
        setSelectedAttributes(prev => ({
            ...prev,
            [category]: value
        }));
    };

    const handleIncrement = () => {
        setQuantity(prev => prev + 1);
    };

    const handleDecrement = () => {
        if (quantity > 1) {
            setQuantity(prev => prev - 1);
        }
    };

    const getAttributeOptions = (category: string) => {
        const attribute = productDetails?.data?.productAttributes?.attributes.find(
            attr => attr.category === category
        );
        return attribute ? attribute.value : [];
    };

    // console.log("attributeCategories",attributeCategories)
    return (
        <>
            <Box>
                {attributeCategories.map(category => (
                    <Box key={category} mb={3}>
                        <h4>{category.charAt(0).toUpperCase() + category.slice(1)}</h4>
                        <Stack direction="row" spacing={1}>
                        {getAttributeOptions(category).map((value: string) => {
                            const isSelected = selectedAttributes[category] === value;
                            return (
                            <Chip
                                key={value}
                                label={value}
                                onClick={() => handleAttributeClick(category, value)}
                                sx={{
                                    color: isSelected ? 'rgba(0, 0, 0, 1)' : 'rgba(119, 126, 144, 1)',
                                    borderColor: 'rgba(230, 232, 236, 1)',
                                    backgroundColor: isSelected ? '' : 'transparent',
                                    ...(isSelected && {
                                        borderColor: 'rgba(230, 232, 236, 1)',
                                        fontWeight: 'bold', 
                                    }),
                                    '&:hover': {
                                        backgroundColor: isSelected ? 'rgba(230, 232, 236, 0.8)' : 'rgba(230, 232, 236, 1)',
                                    },
                                    '& .MuiChip-label': {
                                        fontWeight: isSelected ? 'bold' : 'normal',
                                    }
                                }}
                                variant="outlined"
                            />
                            );
                        })}
                        </Stack>
                    </Box>
                ))}
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                            { matchedSKU?.isAvailable ?
                            <>
                            <Typography sx={{ color: "black", fontSize: "24px" }}>
                                {matchedSKU?.isAvailable ? getCurrency + " " : ""}
                                {matchedSKU?.price?.salePrice}
                            </Typography>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <Box sx={{ 
                                    display: "flex", 
                                    alignItems: "center",
                                    border:"1px solid rgba(244, 245, 246, 1)",
                                    borderRadius:"999px"
                                    }}>
                                <IconButton 
                                    onClick={handleDecrement}
                                    size="small"
                                    disabled={quantity <= 1}
                                    sx={iconButtonStyle}
                                >
                                    <RemoveIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                </IconButton>
                                <Typography 
                                    variant="body1" 
                                    sx={{ px: 2, minWidth: '40px', textAlign: 'center' }}
                                >
                                    {quantity}
                                </Typography>
                                <IconButton 
                                    onClick={handleIncrement} 
                                    size="small"
                                    sx={iconButtonStyle}
                                >
                                    <AddIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                </IconButton>
                                </Box>
                            </Box> 
                            </>
                            : ""
                            }
                        </Box>
                {/* {selectedSkuId && (
                    <Box mt={2}>
                        <p>已選擇 SKU: {selectedSkuId}</p>
                        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                            <Typography sx={{ color: "black", fontSize: "24px" }}>
                                HKD233
                            </Typography>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <IconButton 
                                    onClick={handleDecrement}
                                    size="small"
                                    disabled={quantity <= 1}
                                >
                                    <RemoveIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                </IconButton>
                                <Typography 
                                    variant="body1" 
                                    sx={{ px: 2, minWidth: '40px', textAlign: 'center' }}
                                >
                                    {quantity}
                                </Typography>
                                <IconButton onClick={handleIncrement} size="small">
                                    <AddIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                </IconButton>
                            </Box>
                        </Box>
                    </Box>
                )} */}
            </Box>
        </>
    );
}

export default ProductAttributes;