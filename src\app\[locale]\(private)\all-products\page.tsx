"use client"
import { useLocale, useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMediaQ<PERSON>y,CircularProgress, Pagination, Stack } from "@mui/material";
import SortFilter from "./sort-filter/sortFilter";
import * as React from 'react';
import { styled, Theme } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import ThemeFilter from "./theme-filter/themeFilter";
import RegionFilter from "./region-filter/regionFilter";
import ProductList from "./product-list/productList";
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import {Root} from "@/interface/IincutixProduct"
import { useEffect, useRef, useState } from "react";
import {ISearchCriteria} from "@/interface/ISearchCriteria";
import AddIcon from '@mui/icons-material/Add';
import { RootState, AppDispatch } from '@/redux/stores';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter, usePathname } from 'next/navigation';

interface Props {
    params: {  token: string | null };
}

const AllProducts = ({ params }: Props) => {
//   const Item = styled(Paper)(({ theme }) => ({
//     backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
//     ...theme.typography.body2,
//     padding: theme.spacing(1),
//     textAlign: 'center',
//     color: theme.palette.text.secondary,
//   }));    

  const t = useTranslations("all_product_page");
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
  const [open, setOpen] = useState(false);
  const [productList,setProductList] = useState<Root | undefined>(undefined);
  const [dateSorting,setDateSorting] = useState<string>("desc");
  const [searchCriteria,setSearchCriteria] = useState<ISearchCriteria | undefined>(undefined);
  const [selectedRegions, setSelectedRegions] = useState<string[]>(() => {
    if (typeof window !== 'undefined') {
      const savedRegions = localStorage.getItem('selectedRegions');
      return savedRegions ? JSON.parse(savedRegions) : [];
    }
    return [];
  });
  
  const [selectedThemes, setSelectedThemes] = useState<number[]>(() => {
    if (typeof window !== 'undefined') {
      const savedThemes = localStorage.getItem('selectedThemes');
      return savedThemes ? JSON.parse(savedThemes) : [];
    }
    return [];
  });
  const dispatch = useDispatch<AppDispatch>();
  const {userData, loading, error} = useSelector((state: RootState)=> state.userData);
  const [isRegistering, setIsRegistering] = useState<boolean>(false);
  const [getToken,setGetToken] = useState<string | null>("");
  const [page, setPage] = useState<number>(1);
  const [productPerPage, setProductPerPage] = useState<number>(12);
  // const isRegisteredRef = useRef(false);
  // console.log("userData",userData)
  // console.log("userData error",error)
  // console.log("userData signIn",signIn)
  // console.log("localStorage",localStorage)
  //   const urlParams = new URLSearchParams(window.location.search);
  // const token = urlParams.get('user'); 
  // // console.log(token);
  // console.log("params new check",token)
  // const urlParams = new URLSearchParams(window.location.search);
  // const token = urlParams.get('user'); 
  const router = useRouter();
  const pathname = usePathname();

  const handlePageChange = (page:number) =>{
    setPage(page);
    }

  const handleProductPerPage = (productPerPage:number) =>{
    setProductPerPage(productPerPage);
    setPage(1);
  }

  const handlePaginationChange = (_event: React.ChangeEvent<unknown>, value: number) =>{
    handlePageChange(value);
  }

  const filteredProducts = productList?.data?.items?.filter((value) => {
    const regionMatch = selectedRegions.length === 0 || 
        selectedRegions.some(region => value.region.includes(region));
    
    const themeMatch = selectedThemes.length === 0 ||
        selectedThemes.some(eventId => value.event.id === eventId);
    
    return regionMatch && themeMatch;
  }) || [];

  const startIndex = (page - 1) * productPerPage;
  const currentGetProductDtoList = filteredProducts.slice(startIndex, startIndex + productPerPage);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      const searchParams = new URLSearchParams(url.search);
      const localesParam = searchParams.get('locales');
      
      // Get current path without locale
      const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}\//, '/');
      
      if (localesParam) {
        const newPath = `/${localesParam}${pathWithoutLocale}`;
        const newUrl = `${newPath}?${searchParams.toString()}`;
        
        if (newUrl !== pathname + window.location.search) {
          // Ensure we're redirecting to a valid path
          if (pathWithoutLocale === '/') {
            // If it's the root, redirect to home page with locale
            router.replace(`/${localesParam}/all-products?${searchParams.toString()}`);
          } else {
            router.replace(newUrl);
          }
        }
      }
    }
  }, [pathname, router]);

  useEffect(()=>{
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('user'); 
    setGetToken(token);
    console.log("params new check",token)
    
    const item = {
      token: token,
    };
    localStorage.setItem('userIdToken', JSON.stringify(item));
  },[])
  
  // console.log("params new check getToken",getToken)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedRegions', JSON.stringify(selectedRegions));
    }
  }, [selectedRegions]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedThemes', JSON.stringify(selectedThemes));
    }
  }, [selectedThemes]);

  const filteredCount = productList?.data?.items?.filter((value) => {
    const regionMatch = selectedRegions.length === 0 || 
        selectedRegions.some(region => value.region.includes(region));
    
    const themeMatch = selectedThemes.length === 0 ||
        selectedThemes.some(eventId => value.event.id === eventId);
    
    return regionMatch && themeMatch;
  }).length || 0;

  const currentLocale = useLocale();
  
  const handleDateSortingSelect = (dateSorting:string) =>{
      setDateSorting(dateSorting);
      setPage(1);
  }

  const handleSelectedRegions = (selectedRegions:string[]) =>{
    setSelectedRegions(selectedRegions);
    setPage(1);
  }

  const handleSelectedThemes = (selectedThemes:number[]) =>{
    setSelectedThemes(selectedThemes);
    setPage(1);
  }

  const handleClearSelectedRegions = () => {
    localStorage.removeItem('selectedRegions');
    localStorage.removeItem('selectedThemes');
    setSelectedRegions([]);
    setSelectedThemes([]);
    setPage(1);
  };

  const localeToLanguageCode: Record<string, string> = {
    en: 'EN',
    zh: 'TC',
    th: 'TH'
  };

  const languageCode = localeToLanguageCode[currentLocale] || 'EN';

  const fetchProductList = async () =>{
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/api/public/v1/products?page=1&size=16&language=${languageCode}&sort=saleDate&sortOrder=${dateSorting}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }, 
    });
    const data = await response.json();
    setProductList(data);
  }

  const fetchSearchCriteria = async  () =>{
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/api/public/v1/products/search-criteria?language=${languageCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }, 
    });
    const data = await response.json();
    setSearchCriteria(data);
  }

  const handleToken = async () => {
    if (!getToken) {
      console.log('No token found in URL');
      return;
    }

    try {
      const registeredTokens = JSON.parse(localStorage.getItem('registeredTokens') || '[]');

      // if (isRegisteredRef.current) {
      //   console.log('Token already registered (ref check)');
      //   return;
      // }
      
      if (registeredTokens.includes(getToken)) {
        console.log('Token already registered');
        // isRegisteredRef.current = true;
        return;
      }
      
      if (!userData) {
        console.log('Waiting for user data...');
        return;
      }
;
      setIsRegistering(true);
      try {
        await registerTokenApi();

        localStorage.setItem('registeredTokens', JSON.stringify([...registeredTokens, getToken]));
        // isRegisteredRef.current = true;
      } finally {
        setIsRegistering(false);
      }

    } catch(error) {
      console.error('Authentication error:', error);
      // isRegisteredRef.current = true;
    }
  }

  useEffect(() => {
  if (getToken && userData && !isRegistering) {
    handleToken();
    }
  }, [getToken, userData, isRegistering]);

  const registerTokenApi = async () => {
    try {
      const userIdToken = localStorage.getItem('userIdToken');
      
      if (!userIdToken) {
        console.log('No user token found');
        return;
         }

      // if(shoppingCartItem?.success && userIdToken) {
      //   console.log("exsting user");
      //   return;
      // }
      
      const parsedToken = JSON.parse(userIdToken);
      const userEmail = String(userData!.email);
      const userName = String(userData!.nickName);
      const formatUserId = userData?.userId.replace(
        /^([0-9a-f]{8})([0-9a-f]{4})([0-9a-f]{4})([0-9a-f]{4})([0-9a-f]{12})$/i,
        '$1-$2-$3-$4-$5'
      ).toLowerCase();

      const registerTokenInfo = {
        userId: formatUserId,
        userEmail: userEmail,
        userName: userName,
        token: parsedToken?.token
      };

      const tokenDto = JSON.stringify(registerTokenInfo);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}${process.env.NEXT_PUBLIC_URL_PREFIX}/auth/register-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: tokenDto
      });

      const data = await response.json();
      console.log("register token", data);
      if(data.success){
        localStorage.setItem('registeredTokens', JSON.stringify(userIdToken));
      }
      if(data?.error?.details?.meta?.target.includes("User_email_key")){
        console.log("User Already Register");
      }
    } catch(error) {
      console.error('Error registering token:', error);
    }
  }

  const renderPagination = () =>{
    return(
      <>
      {
        productList?.data.items &&
        (
          <Box sx={{
            display:"flex",
            justifyContent:"center",
            mt:1,
            mb:1
          }}>
            <Stack spacing={2}>
              <Pagination
                  count={Math.ceil(filteredProducts.length / productPerPage)}
                  page={page}
                  onChange={handlePaginationChange}
                  variant="outlined"
                  sx={{
                    '& .MuiPaginationItem-root': {
                      borderRadius: '50%',  
                      color: "rgba(119, 126, 144, 1)",
                      border: '1px solid #FCFCFD', 
                      '&:hover': {
                        border: '1px solid rgba(240, 251, 239, 1)', 
                        backgroundColor: 'rgba(240, 251, 239, 0.1)', 
                      },
                      '&.Mui-selected': {
                        background: 'rgba(240, 251, 239, 1) !important',
                        color: 'rgba(91, 170, 100, 1) !important',
                        border: '1px solid rgba(240, 251, 239, 1) !important', 
                        '&:hover': {
                          background: 'rgba(240, 251, 239, 0.8) !important',
                        },
                      },
                    },
                  }}
                  hidePrevButton hideNextButton
                />
            </Stack>
          </Box>
        )
      }
      </>
    )
  }

  useEffect(() => {
      fetchProductList();
      fetchSearchCriteria();
      setPage(1);
  }, [languageCode,dateSorting]);

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen);
  };
  
  const renderMobileFilterSection = () =>{
    if(isMobile){
      return(
        <>
        <Box sx={{
          paddingRight:2,
          paddingLeft:2,
        }}>
        <h1>{t("all_products")}</h1>
        <Box sx={{
          display:"flex",
          justifyContent:"space-between",
          // mr:2,
          // ml:2
        }}>
        <Button 
          onClick={toggleDrawer(true)}
          sx={{
            borderColor: 'rgba(230, 232, 236, 1)',
            color: 'black',
            backgroundColor: 'transparent',
            '&:hover': {
              borderColor: 'rgba(230, 232, 236, 0.8)',
              backgroundColor: 'rgba(230, 232, 236, 0.1)',
            },
            '&:focus': {
              borderColor: 'rgba(230, 232, 236, 1)',
            },
            borderRadius: '30px',
            padding: '8px 20px', 
            fontSize: '14px',
            textTransform: 'none',
            borderWidth: '1px',
            borderStyle: 'solid',
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            width: 'auto', 
            minWidth: '140px', 
            '& .MuiSvgIcon-root': {
              color: 'black',
              marginLeft: '8px', 
              fontSize: '18px',
            }
          }}
        >
          {t('filter')} <AddIcon />
        </Button>
        <SortFilter dateSorting={dateSorting} handleDateSortingSelect={handleDateSortingSelect}/>
        </Box>
        <p style={{
          color:"rgba(119, 126, 144, 1)"
        }}>{t('total_products', { quantity: filteredCount })}</p>
          <Grid item xs={12} md={8}>
            <Grid container spacing={2}>
                  {
                    currentGetProductDtoList && currentGetProductDtoList
                    
                    .filter((value) => {
   
                      // if (selectedRegions.length === 0) return true;
                      
                      // return selectedRegions.some(region => value.region.includes(region));

                      const regionMatch = selectedRegions.length === 0 || 
                          selectedRegions.some(region => value.region.includes(region));
                      

                      const themeMatch = selectedThemes.length === 0 ||
                          selectedThemes.some(eventId => value.event.id === eventId);
                      
                      return regionMatch && themeMatch; 
                    })
                    
                    .map((value)=>(
                    <Grid item xs={12} sm={6} md={4} key={value.productId}>
                      <ProductList params={params} key={value.productId} items={value}/>
                      </Grid>
                    ))
                  }
            </Grid>
              {renderPagination()}
          </Grid>
        </Box>  
        <Drawer
          anchor="bottom"
          open={open}
          onClose={toggleDrawer(false)}
          sx={{
            '& .MuiDrawer-paper': {
              width: '100%',
              height: '100%',
              maxHeight: '95vh',
              borderTopLeftRadius: '24px',
              borderTopRightRadius: '24px',
              display: 'flex', 
              flexDirection: 'column', 
            },
          }}
        >
          <Box sx={{ display: "flex", justifyContent: "space-between", mr: 2, ml: 2 }}>
            <h4>{t('filter')}</h4>
            <IconButton sx={{ '&:hover': { backgroundColor: 'transparent' } }}>
              <HighlightOffIcon onClick={toggleDrawer(false)} />
            </IconButton>
          </Box>

          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {
              searchCriteria && 
              <ThemeFilter 
              searchCriteria={searchCriteria} 
              handleSelectedThemes={handleSelectedThemes}
              selectedThemes={selectedThemes}
              />
            }
            {
              searchCriteria &&
              <RegionFilter 
              searchCriteria={searchCriteria} 
              handleSelectedRegions={handleSelectedRegions}
              selectedRegions={selectedRegions} 
              />
            }
          </Box>

          <Box sx={{
            display: "flex",
            justifyContent: "space-between",
            mr: 2,
            ml: 2,
            py: 2, 
            position: 'sticky',
            bottom: 0,
            backgroundColor: 'background.paper',
            // borderTop: '1px solid',
            borderColor: 'divider', 
            zIndex: 1, 
          }}>
            <Button onClick={handleClearSelectedRegions}
              sx={{
                borderColor: 'rgba(230, 232, 236, 1)',
                color: 'black',
                backgroundColor: 'transparent',
                '&:hover': {
                  borderColor: 'rgba(230, 232, 236, 0.8)',
                  backgroundColor: 'rgba(230, 232, 236, 0.1)',
                },
                '&:focus': {
                  borderColor: 'rgba(230, 232, 236, 1)',
                },
                borderRadius: '30px',
                padding: '8px 20px', 
                fontSize: '14px',
                textTransform: 'none',
                borderWidth: '1px',
                borderStyle: 'solid',
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                width: 'auto', 
                minWidth: '140px', 
                '& .MuiSvgIcon-root': {
                  color: 'black',
                  marginLeft: '8px', 
                  fontSize: '18px',
                }
              }}
            >{t('clear_all_selection')}</Button>
            <Button 
            variant="contained"
            onClick={toggleDrawer(false)}
            sx={{
                borderColor: 'rgba(230, 232, 236, 1)',
                color: 'white',
                backgroundColor: 'rgba(91, 170, 100, 1)',
                '&:hover': {
                  borderColor: 'rgba(230, 232, 236, 0.8)',
                  backgroundColor: 'rgba(91, 170, 100, 1)',
                },
                '&:focus': {
                  borderColor: 'rgba(230, 232, 236, 1)',
                },
                borderRadius: '30px',
                padding: '8px 30px', 
                fontSize: '14px',
                textTransform: 'none',
                borderWidth: '1px',
                borderStyle: 'solid',
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                width: 'auto', 
                minWidth: '140px', 
                '& .MuiSvgIcon-root': {
                  color: 'black',
                  marginLeft: '8px', 
                  fontSize: '18px',
                }
              }}
            >{t('filtered_products', { filteredCount: filteredCount })}</Button>
          </Box>
        </Drawer>
        </>
      )
    }else if(!isMobile){
      return(
        <>
        <Box sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems:"center",
        padding: 2
      }}>
        <h1>{t("all_products")}</h1>
        <Box sx={{
          display: "flex",
          gap: 2
        }}>
          <p style={{
          color:"rgba(119, 126, 144, 1)"
        }}>{t('total_products', { quantity: filteredCount })}</p>
          <SortFilter dateSorting={dateSorting} handleDateSortingSelect={handleDateSortingSelect}/>
        </Box>
      </Box>
      <Box sx={{ flexGrow: 1,mb:2,padding: 2 }}>
        <Grid container spacing={2}>
           {/* Filter Section (25%) */}
          <Grid item xs={12} md={3}>
            {/* {
              productList?.data?.items &&
              <Button onClick={handleClearSelectedRegions}   style={{ color: 'rgba(119, 126, 144, 1)' }}>{t('clear_all_selection')}</Button>
            } */}
            {
              searchCriteria && 
              <ThemeFilter 
              searchCriteria={searchCriteria} 
              handleSelectedThemes={handleSelectedThemes}
              selectedThemes={selectedThemes}
              />
            }
            {
              searchCriteria &&
              <RegionFilter 
              searchCriteria={searchCriteria} 
              handleSelectedRegions={handleSelectedRegions}
              selectedRegions={selectedRegions} 
              />
            }
          </Grid>
          {/* Product List (75%) */}
          <Grid item xs={12} md={9}>
            <Grid container spacing={2} >
                  {
                    productList?.data?.items ? (currentGetProductDtoList
                    
                    .filter((value) => {
   
                      // if (selectedRegions.length === 0) return true;
                      
                      // return selectedRegions.some(region => value.region.includes(region));
                      
                      const regionMatch = selectedRegions.length === 0 || 
                          selectedRegions.some(region => value.region.includes(region));
                      

                      const themeMatch = selectedThemes.length === 0 ||
                          selectedThemes.some(eventId => value.event.id === eventId);
                      
                      return regionMatch && themeMatch; 
                    })

                    .map((value)=>(
                    <Grid item xs={12} sm={6} md={4} key={value.productId}>
                      <ProductList params={params} key={value.productId} items={value}/>
                      </Grid>
                    ))) : 
                  <Grid container justifyContent="center" alignItems="center" 
                  sx={{
                    height:"50vh",
                    marginRight:{md:"400px"}
                  }}
                  >
                    <CircularProgress sx={{color:"#ff7802"}}/>
                  </Grid>
                  }
            </Grid>
              {renderPagination()}
          </Grid>
        </Grid>
      </Box>
        </>
      )
    }else{
      return(
        <></>
      )
    }
  }

  return (
    <>
      {renderMobileFilterSection()}
    </>
  )
}

export default AllProducts;