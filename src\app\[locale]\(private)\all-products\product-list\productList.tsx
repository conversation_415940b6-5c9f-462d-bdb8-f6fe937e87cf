"use client"
import * as React from 'react';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { useRouter } from 'next/navigation';
import { Box, Tooltip, useMediaQuery } from '@mui/material';
import {Item} from "@/interface/IincutixProduct";



interface Props {
    params?: {  token: string | null };
    items: Item
}

const ProductList = ({ params, items }: Props) =>{
    const router = useRouter();
    const isMobile = useMediaQuery('(max-width:600px)');
    const renderClickEvent = (params?: { token: string | null }) => {
    // const emtpyToken = `${process.env.NEXT_PUBLIC_EMPTY_TOKEN}`;
    // const token = params?.token ?? emtpyToken;
    router.push(`/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}/details/${items.productId}`);
    }
    // console.log("currency iitem",items)
    return(
        <>
        <Box onClick={() => renderClickEvent(params)}>
            <Card sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                cursor: "pointer",
                boxShadow:"none",
            }}>
                <Box sx={{
                    // paddingTop:"40px",
                    // paddingBottom:"40px",
                    // paddingLeft:"10px",
                    // paddingRight:"10px",
                    // padding: isMobile ? "20px" : "40px 10px",
                    backgroundColor:"rgba(243, 243, 243, 1)",
                    borderRadius:'10px',
                    height:286,
                    // width:286
                }}>
                <CardMedia
                    sx={{ 
                        height: isMobile ? 286 : 286,
                        backgroundColor:"rgba(243, 243, 243, 1)",
                        backgroundSize:"cover",
                        borderRadius:'10px',
                    }}
                    image={`${items?.thumbnail}`}
                    title="green iguana"
                />
                </Box>
                <CardContent sx={{ 
                    flexGrow: 1,
                    overflow: 'hidden',
                    px: isMobile ? 1 : 2,
                    py: isMobile ? 1 : 2,
                    backgroundColor:"#FCFCFD"
                }}>
                <Tooltip title={`${items?.event?.name}`}>
                    <Typography 
                        gutterBottom 
                        variant="h5" 
                        component="div" 
                        sx={{ 
                            color: 'text.secondary',
                            fontSize: isMobile ? "10px" : "12px",
                            whiteSpace: 'nowrap', 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis'
                        }}
                    >
                        {items?.event?.name}
                    </Typography>
                </Tooltip>
                <Tooltip title={`${items?.productName}`}>
                    <Typography 
                        variant="body2" 
                        sx={{ 
                            color: 'primary', 
                            mb: 2, 
                            fontSize: isMobile ? "14px" : "16px",
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            fontWeight:"bold"
                        }}
                    >
                        {items?.productName}
                    </Typography>
                </Tooltip>    
                    <Typography 
                        variant="body2" 
                        sx={{ 
                            color: 'primary', 
                            fontSize: isMobile ? "14px" : "16px",
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            fontWeight:"bold"
                        }}
                    >
                        {items?.currency} {items?.price}
                    </Typography>
                </CardContent>
            </Card>
        </Box>
        </>
    )
}

export default ProductList;