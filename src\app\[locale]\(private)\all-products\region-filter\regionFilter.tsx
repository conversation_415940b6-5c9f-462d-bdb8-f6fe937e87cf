"use client"
import React, { useEffect, useState } from 'react';
import { 
  Button, 
  Collapse, 
  Paper, 
  Box
} from '@mui/material';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import {ISearchCriteria,Region} from "@/interface/ISearchCriteria";
import { useTranslations } from 'next-intl';
import ArrowUpIcon from '@/components/icons/ArrowUpIcon';
import ArrowDownIcon from '@/components/icons/ArrowDownIcon';

interface Props {
    searchCriteria: ISearchCriteria;
    handleSelectedRegions:(selectedRegions:string[])=> void;
    selectedRegions: string[];
}

interface IRegionData {
    name: string;
    // Add other properties if your region data has more fields
}


const RegionFilter = ({ searchCriteria,handleSelectedRegions,selectedRegions }: Props) => {
    const [expanded, setExpanded] = useState(false);
    // const [selectedRegionString, setSelectedRegionString] = useState<string[]>([]);
    const t = useTranslations("all_product_page");
    const toggleExpand = () => {
        setExpanded(!expanded);
    };
    const handleRegionChange = (countryCode: string) => {
        // setSelectedRegionString(prev => 
        //     prev.includes(countryCode)
        //         ? prev.filter(code => code !== countryCode)
        //         : [...prev, countryCode]
        // );
        const newSelectedRegions = selectedRegions.includes(countryCode)
            ? selectedRegions.filter(code => code !== countryCode)
            : [...selectedRegions, countryCode];
        handleSelectedRegions(newSelectedRegions);
    };

    const handleClearRegionsSelectedItems = () => {
        localStorage.removeItem('selectedRegions');
        handleSelectedRegions([]);
    };

    const renderClearRegionsButton = () => {
        if (selectedRegions && selectedRegions.length > 0) {
            return (
                <Button 
                    sx={{ 
                        color: 'rgba(119, 126, 144, 1)',
                        paddingLeft: 0,
                        paddingRight: 0,
                        mb: 1
                    }}
                    onClick={handleClearRegionsSelectedItems}
                >
                    {t('clear_all_selection')}
                </Button>
            );
        }
        return null; 
    };

    // Safely get regions data with fallback to empty object
    const regions: Region = searchCriteria?.data?.region || {};

    return (
        <>
        <Box sx={{ margin: 'auto', mt: 2 }}>
        <Button 
            variant="contained" 
            onClick={toggleExpand}
            fullWidth
            sx={{ 
                mb: 1,
                backgroundColor: "transparent",
                color: "black", 
                // borderBottom: "1px solid rgba(230, 232, 236, 1)",
                "&:hover": {
                    backgroundColor: "transparent", 
                    color: "black",
                },
                justifyContent: 'space-between', // 使內容左右分開
            }}
            aria-expanded={expanded}
            aria-label="按主題篩選"
        >
            {/* 左邊：文字 */}
            <span style={{
                fontSize:"16px"
            }}>{t('region')}</span>

            {/* 右邊：數字標籤 + 圖標 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* 圓形數字標籤 */}
                {selectedRegions.length > 0 && (
                    <Box
                        sx={{
                            backgroundColor: "rgba(214, 246, 211, 1)",
                            color: "rgba(91, 170, 100, 1)",
                            borderRadius: "50%", 
                            width: 24,
                            height: 24,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            fontSize: "0.75rem", 
                            fontWeight: 600 
                        }}
                    >
                        {selectedRegions.length}
                    </Box>
                )}

                {/* 動態圖標 */}
                {expanded ? (
                    // <ArrowUpwardIcon fontSize="small" />
                    <ArrowUpIcon sx={{fontSize:"1.5rem"}}/>
                ) : (
                    <ArrowDownIcon sx={{fontSize:"1.5rem"}} />
                )}
            </Box>
        </Button>
        
        <Collapse in={expanded}>
            {/* <Paper elevation={3} sx={{ p: 2 }}> */}
            <Box sx={{
                marginLeft:"15px",
                borderBottom: "2px solid rgba(230, 232, 236, 1)"
            }}>
                <FormGroup sx={{mb:2}}>
                {Object.keys(regions).length > 0 ? (
                    Object.entries(regions).map(([countryCode, regionData]) => (
                        <FormControlLabel 
                            key={countryCode}
                            sx={{
                                '& .MuiFormControlLabel-label': {
                                    fontSize: "16px",
                                },
                            }}
                            control={
                                <Checkbox 
                                    checked={selectedRegions.includes(countryCode)}
                                    onChange={() => handleRegionChange(countryCode)}
                                    sx={{
                                        '&.Mui-checked': {
                                            color: 'rgba(91, 170, 100, 1)', 
                                        },
                                    }}
                                />
                            } 
                            label={regionData?.name} 
                        />
                    ))
                ) : (
                    <div>No regions available</div>
                )}
                </FormGroup>
                {renderClearRegionsButton()}
            </Box>
            {/* </Paper> */}
        </Collapse>
        </Box>
        </>
    )
}

export default RegionFilter;