"use client"
import React, { useEffect, useState } from 'react';
import { 
  Button, 
  Collapse, 
  Paper, 
  Box
} from '@mui/material';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import {ISearchCriteria,Region} from "@/interface/ISearchCriteria";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import { useTranslations } from 'next-intl';

interface Props {
    searchCriteria: ISearchCriteria;
    handleSelectedRegions:(selectedRegions:string[])=> void;
    selectedRegions: string[];
}

interface IRegionData {
    name: string;
    // Add other properties if your region data has more fields
}

const RegionFilter = ({ searchCriteria,handleSelectedRegions,selectedRegions }: Props) => {
    const [expanded, setExpanded] = useState(false);
    // const [selectedRegionString, setSelectedRegionString] = useState<string[]>([]);
    const t = useTranslations("all_product_page");
    const toggleExpand = () => {
        setExpanded(!expanded);
    };
    const handleRegionChange = (countryCode: string) => {
        // setSelectedRegionString(prev => 
        //     prev.includes(countryCode)
        //         ? prev.filter(code => code !== countryCode)
        //         : [...prev, countryCode]
        // );
        const newSelectedRegions = selectedRegions.includes(countryCode)
            ? selectedRegions.filter(code => code !== countryCode)
            : [...selectedRegions, countryCode];
        handleSelectedRegions(newSelectedRegions);
    };

    // handleSelectedRegions(selectedRegionString);

    // Safely get regions data with fallback to empty object
    const regions: Region = searchCriteria?.data?.region || {};
    // console.log("region",regions)
    //     console.log("searchCriteria?",searchCriteria)
    return (
        <>
        <Box sx={{ margin: 'auto', mt: 2 }}>
        {/* <Button 
            variant="contained" 
            onClick={toggleExpand}
            fullWidth
            sx={{ mb: 1 }}
        >
            地區{selectedRegions.length > 0 && `(${selectedRegions.length})`}
        </Button> */}
        <Button 
            variant="contained" 
            onClick={toggleExpand}
            fullWidth
            sx={{ 
                mb: 1,
                backgroundColor: "white",
                color: "black", 
                // borderBottom: "1px solid rgba(230, 232, 236, 1)",
                "&:hover": {
                    backgroundColor: "white", 
                    color: "black",
                },
                justifyContent: 'space-between', // 使內容左右分開
            }}
            aria-expanded={expanded}
            aria-label="按主題篩選"
        >
            {/* 左邊：文字 */}
            <span>{t('region')}</span>

            {/* 右邊：數字標籤 + 圖標 */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* 圓形數字標籤 */}
                {selectedRegions.length > 0 && (
                    <Box
                        sx={{
                            backgroundColor: "rgba(214, 246, 211, 1)", // 背景色
                            color: "rgba(91, 170, 100, 1)",            // 文字色
                            borderRadius: "50%",                       // 圓形
                            width: 24,
                            height: 24,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            fontSize: "0.75rem",                       // 調整字體大小
                        }}
                    >
                        {selectedRegions.length}
                    </Box>
                )}

                {/* 動態圖標 */}
                {expanded ? (
                    <ArrowUpwardIcon fontSize="small" />
                ) : (
                    <ArrowBackIcon fontSize="small" />
                )}
            </Box>
        </Button>
        
        <Collapse in={expanded}>
            {/* <Paper elevation={3} sx={{ p: 2 }}> */}
            <Box sx={{
                marginLeft:"15px",
                borderBottom: "2px solid rgba(230, 232, 236, 1)"
            }}>
                <FormGroup sx={{mb:2}}>
                {Object.keys(regions).length > 0 ? (
                    Object.entries(regions).map(([countryCode, regionData]) => (
                        <FormControlLabel 
                            key={countryCode}
                            control={
                                <Checkbox 
                                    checked={selectedRegions.includes(countryCode)}
                                    onChange={() => handleRegionChange(countryCode)}
                                    sx={{
                                        '&.Mui-checked': {
                                            color: 'rgba(91, 170, 100, 1)', 
                                        },
                                    }}
                                />
                            } 
                            label={regionData?.name} 
                        />
                    ))
                ) : (
                    <div>No regions available</div>
                )}
                </FormGroup>
            </Box>    
            {/* </Paper> */}
        </Collapse>
        </Box>
        </>
    )
}

export default RegionFilter;