"use client"
import * as React from 'react';
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import { useTranslations } from 'next-intl';

interface Props{
    dateSorting:string;
    handleDateSortingSelect:(dateSorting:string)=> void;
}

const SortFilter = ({dateSorting,handleDateSortingSelect}:Props) =>{

    // const [age, setAge] = React.useState('');

    // const handleChange = (event: SelectChangeEvent) => {
    //   setAge(event.target.value as string);
    // };
    const t = useTranslations("all_product_page");
    const handleChange = (event: SelectChangeEvent) => {
        handleDateSortingSelect(event.target.value);
    }
  
    return(
        <>
<Box sx={{ minWidth: 120 }}>
  <FormControl fullWidth>
    <Select
      labelId="demo-simple-select-label"
      id="demo-simple-select"
      value={dateSorting}
      onChange={handleChange}
      sx={{
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: 'rgba(230, 232, 236, 1)', // 邊框顏色
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: 'rgba(230, 232, 236, 1)', // 鼠標懸停時的邊框顏色
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: 'rgba(230, 232, 236, 1)', // 聚焦時的邊框顏色
        },
        '& .MuiSelect-icon': {
          color: 'black', // 下拉圖標顏色
        },
        borderRadius:"30px",
        paddingRight:"40px",
        fontSize:"14px"
      }}
    >
      <MenuItem 
        value={"desc"}
        sx={{
          '&.Mui-selected': {
            backgroundColor: '#ff7802', // 選中項背景色
            color: '#fff', // 選中項文字顏色
          },
          '&.Mui-selected:hover': {
            backgroundColor: '#ff7802', // 選中項懸停背景色
          },
          fontSize:"14px"
        }}
      >
        {t('new_to_old')}
      </MenuItem>
      <MenuItem 
        value={"asc"}
        sx={{
          '&.Mui-selected': {
            backgroundColor: '#ff7802', // 選中項背景色
            color: '#fff', // 選中項文字顏色
          },
          '&.Mui-selected:hover': {
            backgroundColor: '#ff7802', // 選中項懸停背景色
          },
          fontSize:"14px"
        }}
      >
        {t('old_to_new')}
      </MenuItem>
    </Select>
  </FormControl>
</Box>
        </>
    )
}

export default SortFilter;