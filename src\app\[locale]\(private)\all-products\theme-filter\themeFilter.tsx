"use client"
import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Collapse, 
  Paper, 
  Typography,
  Box,
  FormGroup,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { ISearchCriteria } from "@/interface/ISearchCriteria";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import { useTranslations } from 'next-intl';

interface Props {
    searchCriteria: ISearchCriteria;
    handleSelectedThemes:(selectedThemes:number[])=> void;
    selectedThemes:number[];
}

const ThemeFilter = ({searchCriteria,handleSelectedThemes,selectedThemes}: Props) => {
    const [expanded, setExpanded] = useState(false);
    const t = useTranslations("all_product_page");
    const toggleExpand = () => {
        setExpanded(!expanded);
    };

    const handleThemeChange = (eventId: number) => {
        // setSelectedThemes(prev => 
        //     prev.includes(eventId)
        //         ? prev.filter(id => id !== eventId)
        //         : [...prev, eventId]
        // );
        const newSelectedThemes = selectedThemes.includes(eventId)
            ? selectedThemes.filter(id => id !== eventId)
            : [...selectedThemes, eventId];
        handleSelectedThemes(newSelectedThemes);
    };
    
    // handleSelectedThemes(selectedThemes);

    const events = searchCriteria?.data?.events || [];

    return (
        <Box sx={{ margin: 'auto', mt: 2 }}>
            {/* <Button 
                variant="contained" 
                onClick={toggleExpand}
                fullWidth
                sx={{ 
                    mb: 1,
                    backgroundColor: "white",
                    color: "black", 
                    borderBottom: "1px solid rgba(230, 232, 236, 1)",
                    "&:hover": {
                        backgroundColor: "white", 
                        color: "black" 
                    }
                }}
                aria-expanded={expanded}
                aria-label="按主題篩選"
            >
                主題 {selectedThemes.length > 0 && `(${selectedThemes.length})`}
            </Button> */}
            <Button 
                variant="contained" 
                onClick={toggleExpand}
                fullWidth
                sx={{ 
                    mb: 1,
                    backgroundColor: "white",
                    color: "black", 
                    // borderBottom: "1px solid rgba(230, 232, 236, 1)",
                    "&:hover": {
                        backgroundColor: "white", 
                        color: "black",
                    },
                    justifyContent: 'space-between', // 使內容左右分開
                }}
                aria-expanded={expanded}
                aria-label="按主題篩選"
            >
                {/* 左邊：文字 */}
                <span>{t('theme')}</span>

                {/* 右邊：數字標籤 + 圖標 */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {/* 圓形數字標籤 */}
                    {selectedThemes.length > 0 && (
                        <Box
                            sx={{
                                backgroundColor: "rgba(214, 246, 211, 1)", // 背景色
                                color: "rgba(91, 170, 100, 1)",            // 文字色
                                borderRadius: "50%",                       // 圓形
                                width: 24,
                                height: 24,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                fontSize: "0.75rem",                       // 調整字體大小
                            }}
                        >
                            {selectedThemes.length}
                        </Box>
                    )}

                    {/* 動態圖標 */}
                    {expanded ? (
                        <ArrowUpwardIcon fontSize="small" />
                    ) : (
                        <ArrowBackIcon fontSize="small" />
                    )}
                </Box>
            </Button>
            
            <Collapse in={expanded}>
                {/* <Paper elevation={3} sx={{ p: 2 }}> */}
                <Box sx={{
                        marginLeft:"15px",
                        borderBottom: "2px solid rgba(230, 232, 236, 1)"
                    }}>
                    {events.length > 0 ? (
                        <FormGroup sx={{mb:2}}>
                            {events.map(event => (
                                <FormControlLabel 
                                    key={event.eventId}
                                    control={
                                        <Checkbox 
                                            checked={selectedThemes.includes(event.eventId)}
                                            onChange={() => handleThemeChange(event.eventId)}
                                            sx={{
                                                '&.Mui-checked': {
                                                    color: 'rgba(91, 170, 100, 1)', 
                                                },
                                            }}
                                        />
                                    } 
                                    label={
                                        <Typography variant="body2">
                                            {event.eventName}
                                        </Typography>
                                    } 
                                />
                            ))}
                        </FormGroup>
                    ) : (
                        <Typography variant="body2" color="text.secondary">
                            暫無主題數據
                        </Typography>
                    )}
                {/* </Paper> */}
                </Box>
            </Collapse>
        </Box>
    );
};

export default ThemeFilter;