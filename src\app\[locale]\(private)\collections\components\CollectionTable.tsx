"use client";
import React, { useMemo, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Button, Typography, Avatar } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { useRouter } from "next/navigation";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { format } from "date-fns";
import { ROUTES } from "@/utils/constants";
import { ICollection } from "@/interface/ICollection";
import PageSelector from "@/components/PageSelector";
import SearchInput from "@/components/input/SearchInput";
import SortBySelector, { SortOption } from "@/components/SortBySelector";

import EditButton from "@/components/buttons/EditButton";
import DeleteCollection from "./DeleteCollection";

const sortByOptions: SortOption[] = [
  {
    labelKey: "collection.sortby_collection_id_desc",
    id: "id",
    order: "desc",
  },
  {
    labelKey: "collection.sortby_collection_name_asc",
    id: "name",
    order: "asc",
  },
  {
    labelKey: "collection.sortby_collection_name_desc",
    id: "name",
    order: "desc",
  },
  {
    labelKey: "collection.sortby_created_at_desc",
    id: "createdAt",
    order: "desc",
  },
  {
    labelKey: "collection.sortby_created_at_asc",
    id: "createdAt",
    order: "asc",
  },
];

const CollectionTable = () => {
  const t = useTranslations("");
  const [data, setData] = useState()
  const router = useRouter();
  const [keyword, setKeyword] = React.useState("");
  const [sorting, setSorting] = React.useState<SortOption>(sortByOptions[0]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data: collections,
    isLoading
  } = useQuery({
    queryKey: ["collections", { pagination, sorting, keyword }],
    queryFn: async () =>
      xior
        .get<IListResponse<ICollection>>("/api/collections", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
            sortby: sorting.id,
            order: sorting.order,
            keyword,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<ICollection>[]>(
    () => [
      {
        accessorKey: "collectionId",
        header: "",
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            gap={1}
          >
            <EditButton
              iconOnly={true}
              onClick={() => router.push(`${ROUTES.COLLECTION}/${data.row.original.id}/edit`)}
            />
            <DeleteCollection collection={data.row.original} />

          </Box>
        ),
      },
      {
        accessorKey: "photoUrl",
        header: t("collection.label_thumbnail"),
        cell: (data) =>
          data.getValue() ? (
            <Box
              component={"img"}
              src={data.getValue() as string}
              alt="collection-thumbnail"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          ) : (
            <Box 
              component={"img"}
              src="/images/image_not_found.png"
              alt="image-not-found"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          )
      },
      {
        accessorKey: "name",
        header: t("collection.label_collection_name"),
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
          >
            <Typography>{data.getValue()}</Typography>
          </Box>
        ),
      },
      {
        accessorKey: "product",
        header: t("collection.label_product"),
        cell: (data) => {
          return data.getValue() && data.getValue() as number > 0? data.getValue() : 0
        }
      },
      {
        accessorKey: "updatedAt",
        header: t("collection.label_last_update"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
      {
        accessorKey: "createdAt",
        header: t("collection.label_created_at"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
    ],
    [router, t]
  );

  const defaultData = React.useMemo<ICollection[]>(() => [], []);

  const table = useReactTable({
    data: collections?.items ?? defaultData,
    columns,
    rowCount: collections?.count,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  return (
    <Box display={"flex"} flexDirection={"column"} height={"100%"}>
      <Box display={"flex"} flexDirection={"row"} mb={3}>
        <Box flex={1} margin={0}>
          <SearchInput
            value={keyword}
            onChange={(value) => {
              setKeyword(value);
            }}
          />
        </Box>
        <SortBySelector
          options={sortByOptions}
          value={sorting}
          onChange={(value) => setSorting(value)}
        />
        <PageSelector
          value={pagination.pageSize}
          onChange={(newPageSize) => {
            table.setPageSize(newPageSize);
          }}
        />
      </Box>
      <Box flex={1}>
        <PaginationTable table={table} fullWidth isLoading={isLoading} msg={t("collection.title_empty")}/>
      </Box>
    </Box>
  );
};

export default CollectionTable;
