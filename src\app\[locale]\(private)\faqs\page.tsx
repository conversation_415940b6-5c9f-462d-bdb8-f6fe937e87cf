"use client"
import React, { useRef, useState } from 'react';
import {
  Tabs,
  Tab,
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useTranslations } from 'next-intl';

  const Faqs = () =>{
    const t = useTranslations("faqs");
    const [value, setValue] = useState(0);
    const [expanded, setExpanded] = useState<string | null>(null); 
  
    const faqRefs = [
      useRef<HTMLDivElement>(null),
      useRef<HTMLDivElement>(null),
      useRef<HTMLDivElement>(null),
      useRef<HTMLDivElement>(null),
      useRef<HTMLDivElement>(null),
    ];
  
    const tabStyled = {
      '&.Mui-selected': {
          color: 'rgba(79, 183, 71, 1)', 
          borderColor: 'rgba(79, 183, 71, 1)',
      },
  }

    // console.log('tab',value)
    const handleChange = (event:any, newValue:any) => {
      setValue(newValue);
      setExpanded(`faq-${newValue}`);

      if (faqRefs[newValue].current) {
        faqRefs[newValue].current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    };
  
    const handleAccordionChange = (panel:string) => (event:React.SyntheticEvent, isExpanded:boolean) => {
      setExpanded(isExpanded ? panel : null);
    };

    const faqsText ={
      mb:2,
      mt:2,
      fontSize: "24px",
      fontWeight: 'bold'
    }

    const faqsSectionTitle = {
      fontSize: "16px"
    }

    const faqsAnswerText ={
      fontSize: "16px",
      color:"rgba(119, 126, 144, 1)"
    }
    
    return(
        // <>
        //   <Box sx={{
        //       paddingRight:2,
        //       paddingLeft:2,
        //       mb:2
        //     }}>
        //   <Typography variant="h2" sx={{
        //     mt:2,
        //     mb:2
        //   }}>常見問題</Typography>
        //   <Box display="flex" flexDirection="row" p={2}   
        //   sx={{
        //     position: 'relative', // 确保 sticky 的参考容器正确
        //     alignItems: 'flex-start' // 防止垂直拉伸
        //   }}>
        //     {/* 左側的分類 Tab */}
        //     {/* <Paper elevation={3} style={{ width: '20%', marginRight: '16px' }}> */}
        //       <Tabs
        //         orientation="vertical"
        //         variant="scrollable"
        //         value={value}
        //         onChange={handleChange}
        //         aria-label="FAQ categories"
        //         TabIndicatorProps={{
        //           style: { backgroundColor: 'rgba(79, 183, 71, 1)' },
        //       }}
        //       sx={{
        //         position: 'sticky',
        //         top: 80, // 根据你的导航栏高度调整
        //         height: 'fit-content',
        //         alignSelf: 'flex-start', // 防止被拉伸
        //         ...tabStyled
        //       }}
        //       >
        //         <Tab label="購票流程" sx={tabStyled}/>
        //         <Tab label="註冊及登入相關" sx={tabStyled}/>
        //         <Tab label="購買票證相關" sx={tabStyled}/>
        //         <Tab label="購買周邊商品" sx={tabStyled}/>
        //         <Tab label="技術支援" sx={tabStyled}/>
        //       </Tabs>
        //     {/* </Paper> */}

        //     {/* 右側的 FAQ 問題 */}
        //     <Box sx={{
        //       flex: 1,
        //       overflowY: 'auto', 
        //       maxHeight: 'calc(100vh - 160px)', 
        //       paddingLeft:2,
        //       paddingRight:2,
        //       width: '100%'
        //     }}
        //     >
        //     <Typography variant="h6" sx={faqsText}  ref={faqRefs[0]}>購票流程</Typography>
            
        //       <Accordion
        //         expanded={expanded === 'faq-0'}
        //         onChange={handleAccordionChange('faq-0')}
        //       >
        //         <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //           <Typography variant="body1" sx={faqsSectionTitle}>1.1 完整流程</Typography>
        //         </AccordionSummary>
        //         <AccordionDetails>
        //           <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //             A: Lorem Ipsum is simply dummy text of the printing and typesetting industry.
        //           </Typography>
        //         </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //         <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //           <Typography variant="body1" sx={faqsSectionTitle}>1.2 註冊會員</Typography>
        //         </AccordionSummary>
        //         <AccordionDetails>
        //           <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //             A: You can create an account by clicking on the "Sign Up" button.
        //           </Typography>
        //         </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //         <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //           <Typography variant="h6" sx={faqsSectionTitle}>1.3 購票票證</Typography>
        //         </AccordionSummary>
        //         <AccordionDetails>
        //           <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //             A: We accept credit cards, PayPal, and bank transfers.
        //           </Typography>
        //         </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //         <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //           <Typography variant="h6" sx={faqsSectionTitle}>1.4 購買周邊商品</Typography>
        //         </AccordionSummary>
        //         <AccordionDetails>
        //           <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //             A: You can reset your password by clicking on the "Forgot Password" link.
        //           </Typography>
        //         </AccordionDetails>
        //       </Accordion>
        //       <Typography variant="h6" sx={faqsText} ref={faqRefs[1]}>註冊及登入相關</Typography>

        //       <Accordion
        //       expanded={expanded === 'faq-1'}
        //       onChange={handleAccordionChange('faq-1')}
        //       >
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //         <Typography variant="body1" sx={faqsSectionTitle}>2.1 我要進行電郵驗證, 但收不到電郵怎麼辦?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //         <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //           A: Lorem Ipsum is simply dummy text of the printing and typesetting industry.
        //         </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //         <Typography variant="body1" sx={faqsSectionTitle}>2.2 忘記密碼怎樣辦?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //         <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //           A: You can create an account by clicking on the "Sign Up" button.
        //         </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //         <Typography variant="h6" sx={faqsSectionTitle}>2.3 如何更改密碼?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //         <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //           A: We accept credit cards, PayPal, and bank transfers.
        //         </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //         <Typography variant="h6" sx={faqsSectionTitle}>2.4 註冊時我沒有即時驗證電郵地址, 稍後如何驗證帳戶?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //         <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //           A: You can reset your password by clicking on the "Forgot Password" link.
        //         </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Typography variant="h6" sx={faqsText} ref={faqRefs[2]}>購買票證相關</Typography>

        //       <Accordion
        //       expanded={expanded === 'faq-2'}
        //       onChange={handleAccordionChange('faq-2')}
        //       >
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="body1" sx={faqsSectionTitle}>3.1 我要可以取消或退款已購買票證嗎?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: Lorem Ipsum is simply dummy text of the printing and typesetting industry.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="body1" sx={faqsSectionTitle}>3.2 如何用Visa/Mastercard 銀聯付款被拒絕怎麼辦?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: You can create an account by clicking on the "Sign Up" button.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="h6" sx={faqsSectionTitle}>3.3 我們接手哪些付款方式?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: We accept credit cards, PayPal, and bank transfers.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="h6" sx={faqsSectionTitle}>3.4 如何確認已成功購買票證?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: You can reset your password by clicking on the "Forgot Password" link.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="h6" sx={faqsSectionTitle}>3.5 如何在結帳時使用推廣碼? </Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: You can reset your password by clicking on the "Forgot Password" link.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="h6" sx={faqsSectionTitle}>3.6 如果推廣碼無法使用，該怎麼辦? </Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: You can reset your password by clicking on the "Forgot Password" link.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Typography variant="h6" sx={faqsText} ref={faqRefs[3]}>購買周邊商品</Typography>

        //       <Accordion
        //       expanded={expanded === 'faq-3'}
        //       onChange={handleAccordionChange('faq-3')}
        //       >
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="body1" sx={faqsSectionTitle}>3.1 我需要先成為 INCUTIX 會員才能購買周邊商品嗎?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: Lorem Ipsum is simply dummy text of the printing and typesetting industry.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="body1" sx={faqsSectionTitle}>3.2 如果我收到的商品有問題該怎麼辦?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: You can create an account by clicking on the "Sign Up" button.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Accordion>
        //       <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        //       <Typography variant="h6" sx={faqsSectionTitle}>3.3 我如何領取我的預購商品?</Typography>
        //       </AccordionSummary>
        //       <AccordionDetails>
        //       <Typography variant="body2" gutterBottom sx={faqsAnswerText}>
        //       A: We accept credit cards, PayPal, and bank transfers.
        //       </Typography>
        //       </AccordionDetails>
        //       </Accordion>

        //       <Typography variant="h6" sx={faqsText} ref={faqRefs[4]}>技術支援</Typography>
        //     </Box>
        //   </Box>
        //   </Box>
        // </>
        <></>
    );
  };

  export default Faqs;