"use client"
import { useTranslations } from 'next-intl';
//   import LanguageSwitcher from '@/components/LanguageSwitcher';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Box, Button, IconButton, Typography } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import ImagesSlider from '@/components/ImagesSlider';
import FeaturedEvent from '@/components/featured-events/page';


const Home = () =>{

    const images = [
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/51656209e3952cbd393753000c3713b7a6cc9386.jpeg',
        'https://s3.ap-southeast-1.amazonaws.com/portal-dev.fun-verse.io/productThumbnail/tmp/1e0fdb9db6d47e726d9ea69a7a31476892c65c2c.jpeg',
        'https://assets.incutix.com/events/one_piece_tour_thailand/OPTH_KV_Horizontal.jpg',
        'https://assets.incutix.com/events/JuJuTsu_Kaisen_Exhibition_my/jjk_my_kv_20240813.jpg',
      ];
        //New Nick
       const originalToken = "NGlpbmplWUpVSGlHZVk2TTFhcGlvMG9mM0NPK3VKRWRQNDkrYkNvZmdtREd0VUYxaGp6T0M1SkdHTWZlbUhBag==";

       
       //john_doe
       //const originalToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIXHhXOed8WopQbTLNb8Wn.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
       //jane_smith
       //const originalToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIK0ixNqfPl28Jnw3ebHGE.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
       //JustWantSomeNameToTest123
       //const originalToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6IjVhZjhiZmIzLWRjYjYtNDcwYS05YzQ4LTM2NDdmYWQ3YmE2MyJ9.xVPr61N0ksq9OH7rf8S2acYdd3QDXhOR-3ahezTAo0A";
       const raymondToken = "bWMzRkFVTHJ2QUVVZXMrL3d3eFNsQmRSOXU0RzgxMXdhenE2TjZEN2FTTG9QUWJhallMSkNEOFlLQnlvMXlsNg==";
       

        // const base64Encoded = btoa(originalToken); 
        // const urlSafeEncoded = encodeURIComponent(base64Encoded);

        // const base64Encoded2 = btoa(raymondToken); 
        // const urlSafeEncoded2 = encodeURIComponent(base64Encoded2);
       
        const handleClickFunction = () =>{
            window.location.href = `${process.env.NEXT_PUBLIC_NEXTAUTH_URL_MARKET}/all-products/${originalToken}`;
        }

        const handleRaymondLogin = () =>{
            window.location.href = `${process.env.NEXT_PUBLIC_NEXTAUTH_URL_MARKET}/all-products/${raymondToken}`;
        }

        const removeToken = () => {
            localStorage.removeItem("userIdToken");
            localStorage.removeItem("registeredTokens");
        }
    return(
        <>
        <div>
            <h1>Token Verification Link</h1>
            <Button onClick={handleClickFunction}>Demo Nick login</Button>
            <Button onClick={handleRaymondLogin}>Demo Miracle login</Button>
            <br></br><br></br>
            {/* <a onClick={removeToken}>Remove Token</a> */}
            <Button onClick={removeToken}>Demo logout</Button>
            <p>Or copy this URL:</p>
            {/* <code>{verificationLink}</code> */}
        </div>
        <ImagesSlider
            images={images}
            interval={5000} 
            height="400px" 
            blurIntensity={100} 
        />
        <FeaturedEvent/>
        </>
    )
}

export default Home;

// function generateCodeFromToken(token: string) {
//     throw new Error('Function not implemented.');
// }
