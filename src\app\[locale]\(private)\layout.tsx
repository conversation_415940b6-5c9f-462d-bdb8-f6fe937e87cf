import * as React from "react";
import { Box } from "@mui/material";
import Header from "@/components/Header";
import SideBar from "@/components/Sidebar";
import Footer from "@/components/Footer";
import { COLORS } from "@/styles/colors";
import AccountMenu from "@/components/buttons/AccountMenu";

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
    <Box
      display={"flex"}
      flexDirection={"column"}
      minHeight="100vh"
      sx={{ background: "#FCFCFD" }}
    >  
      <Header />
      <Box
      // display={"flex"} 
      display={"contents"} 
      flexDirection={"row"} 
      flex={1}>
      {/* <SideBar /> */}
        <Box flex={1} marginLeft={0.5} sx={{ 
          background: "#FCFCFD",
          maxWidth: 1280, 
          width: "100%", 
          margin: "0 auto",
          }}>
          {children}
        </Box>
      </Box>
      <Footer />
    </Box>
  );
}
