"use client";
import ModalContainer from "@/components/ModalContainer";
import AddNewButton from "@/components/buttons/AddNewButton";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import TextField from "@/components/input/TextField";
import { MemberGroupSchema } from "@/schemas/MemberGroupSchema";
import { COLORS } from "@/styles/colors";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography, Select, MenuItem } from "@mui/material";
import { useQueryClient, useQuery, keepPreviousData } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";
import CloseIcon from '@mui/icons-material/Close';
import { IListResponse } from "@/interface/IListResponse";
import { IMemberGroup } from "@/interface/IMemberGroup";
import { ChangePasswordSchema } from "@/schemas/ChangePasswordSchema";
import { InvitationSchema } from "@/schemas/InvitationSchema";
import EditButton from "@/components/buttons/EditButton";

type FormValue = z.infer<typeof MemberGroupSchema>;

const EditInvitation = ({ invitation, refetch }: { invitation: any, refetch: Function }) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const [groups, setGroups] = React.useState<Array<{ id?: number, label: string, value: number }>>([])
  const [selectedGroups, setSelectedGroups] = React.useState<Array<any>>([])
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
    reset,
  } = useForm<FormValue>({
    resolver: zodResolver(InvitationSchema),
    defaultValues: {
      name: "",
      email: "",
      groups: [],
    },
  });


  const dataQuery = useQuery({
    queryKey: ["member_groups"],
    queryFn: async () =>
      xior
        .get<IListResponse<IMemberGroup>>("/api/member-groups",)
        .then((res) => {
          return res.data;
        }),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  React.useEffect(() => {
    let groups;
    if (dataQuery?.data?.items) {
      const {
        items = []
      } = dataQuery.data
      setGroups(items.map((item) => ({
        label: item.name,
        value: item.id
      })))
      if (invitation.memberGroups) {
        groups = invitation?.memberGroups?.split(",")
        groups = groups.map((group: string) => {
          const target = items.find((item: { name: string }) => item.name === group)
          if (target) return target.id
          return false
        }).filter((item: number | boolean) => item)
      }
    }
    reset({ ...invitation, groups });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataQuery?.data?.items, open, invitation, reset])

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
    setSelectedGroups([])
  }, [isSubmitting]);

  const handleOnChange = (event: any) => {
    const {
      value
    } = event.target
    setSelectedGroups([...new Set([
      ...value
    ])] as never[])
  }

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const allGroups = groups.map((group) => {
        if ((selectedGroups).includes(group.value)) return group.value
        return undefined
      }).filter(item => item)
      const body = {
        ...data,
        groups: allGroups
      }
      await xior.put(`/api/members/invitation/${invitation.id}`, body);
      queryClient.invalidateQueries({ queryKey: ["invitation"] });
      setTimeout(() => {
        refetch()
      }, 1000)
      reset();
      setOpen(false);
      setSelectedGroups([])
    } catch (e) {
      setError("name", { type: "custom", message: "unknown error" });
    }
  };

  return (
    <Box>
      <EditButton iconOnly sx={{ marginRight: 1.5 }} onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("invitation.title_create_invitation")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                required
                value={field.value}
                onChange={field.onChange}
                disabled={isSubmitting}
                label={t("invitation.label_new_member_name")}
                error={errors?.name?.message}
              />
            )}
          />
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                required
                value={field.value}
                onChange={field.onChange}
                disabled={isSubmitting}
                label={t("invitation.label_email")}
                error={errors?.email?.message}
              />
            )}
          />
          <Controller
            name="groups"
            control={control}
            render={({ field }) => (
              <>
                <Typography fontSize={14}>
                  {t("invitation.label_group")}
                </Typography>
                <Select
                  multiple={true}
                  disabled={isSubmitting}
                  error={!!errors?.groups?.message}
                  value={field.value || []}
                  onChange={(event) => {
                    handleOnChange(event)
                    field.onChange(event)
                  }}
                  size="small"
                >
                  {
                    groups.map((group) => (
                      <MenuItem key={group.id} value={group.value}>{group.label}</MenuItem>
                    ))
                  }
                </Select>
              </>
            )}
          />
          <br />
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton
              disabled={isSubmitting}
              onAction={handleClose}
            />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditInvitation;
