"use client"
import { <PERSON>, <PERSON><PERSON>, Container, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Typo<PERSON> } from "@mui/material";
import React, { useEffect, useState } from 'react';
import ErrorIcon from '@mui/icons-material/Error';
import { useRouter } from 'next/navigation';
import { useTranslations } from "next-intl";

const greenButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'white',
  backgroundColor: 'rgba(91, 170, 100, 1)',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(91, 170, 100, 1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
//   display: 'flex', 
//   justifyContent: 'space-between', 
  alignItems: 'center', 
//   width: '100%', 
    minWidth: '140px', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }  
}

interface Props{
    params:{
        id:string;
    }
}


const PaymentCallbackPage = ({params}:Props) =>{
    const [orderNo, setOrderNo] = useState<string>('');
    const [txnState, setTxnState] = useState<string | null>(null);
    const t = useTranslations("payment_callback_page");
    const router = useRouter();
    const handleHomeTab = () => {
        const url = process.env.NEXTAUTH_URL || '/'; 
        router.push(url);
    };
    const handleReturnOrderPage = () =>{
        const baseUrl = process.env.NEXTAUTH_URL || '';
        router.push(`${baseUrl}/order/${params.id}`);
    }
    // console.log("params",params.id)
    useEffect(() => {
        const currentUrl = window.location.href;
        const url = new URL(currentUrl);
        const merchantOrderNo = url.searchParams.get('merchant_order_no');
        const txnStateFromUrl = url.searchParams.get('txn_state');
        if (merchantOrderNo) setOrderNo(merchantOrderNo)
        if(txnStateFromUrl) setTxnState(txnStateFromUrl)
    }, [])


    const renderPaymentStatusMessage = () =>{

        if(txnState?.includes("SUCCESS")){
            return(
                <>
                <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="85vh"
                    flexDirection="column"
                    sx={{
                        marginTop: "40px",
                        marginRight: "100px",
                        marginLeft: "100px"
                    }}
                >
                    {/* <IconButton sx={{ color: "rgba(195, 214, 82, 1)" }}>
                        <CheckCircleOutlineIcon fontSize="large" />
                    </IconButton> */}
                    <img src={"/images/SUCCESS.png"} width="100px"/>
                    <Typography sx={{fontSize:"32px",mt:2}} variant="h2">{t('payment_success')}</Typography><br />
                    <Typography sx={{color:"rgba(91, 170, 100, 1)",fontSize:"16px"}}>{t('order_no')}: #{orderNo}</Typography><br />
                    <Typography sx={{fontSize:"16px",color:"rgba(130, 130, 130, 1)"}}>{t('confirm_message')}</Typography>
                    <Typography sx={{fontSize:"16px",color:"rgba(130, 130, 130, 1)"}}>{t('orderDetailsMessage')}</Typography><br />
                    <Button onClick={handleHomeTab} sx={greenButtonCss}>{t('returnHome')}</Button>
                </Box>
                </>
            )
        }else if(txnState?.includes("PAYERROR")){
            return(
                <>
                    <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="85vh"
                    flexDirection="column"
                    sx={{
                        marginTop: "40px",
                        marginRight: "100px",
                        marginLeft: "100px"
                        }}
                    >
                    <img src={"/images/PAYERROR.png"} width="100px"/>
                        <Typography sx={{fontSize:"32px",mt:2}} variant="h2">{t('payment_failed')}</Typography><br />
                        <Typography sx={{color:"rgba(91, 170, 100, 1)",fontSize:"16px"}}>{t('order_no')}: #{orderNo}</Typography><br />
                        <Typography sx={{fontSize:"16px",color:"rgba(0, 0, 0, 1)"}}>{t('contact_cs')}</Typography>
                        <Box sx={{display:"flex",mb:2}}>
                        <Typography sx={{fontSize:"16px",color:"rgba(0, 0, 0, 1)"}}>{t('email')}：</Typography>
                        <Link href="mailto:<EMAIL>" color="inherit" sx={{textDecoration: "none"}}>
                            <EMAIL>
                        </Link><br />
                        </Box>
                        {/* <Button  variant="contained" onClick={() => {
                             console.log("History length:", window.history.length); // Log history length
                            window.history.go(-2)}}>Back</Button> */}
                        <Button sx={greenButtonCss} onClick={handleReturnOrderPage}>
                            {t('returnCheckOut')}
                        </Button>
                    </Box>
                </>
            )
        }else{
            return(
                <>
                </>
            )
        }
    }



    return (
        <div>
            <Container sx={{mt:2,mb:2}}>
                {
                    renderPaymentStatusMessage()
                }
            </Container>
        </div>
    );
}

export default PaymentCallbackPage;
