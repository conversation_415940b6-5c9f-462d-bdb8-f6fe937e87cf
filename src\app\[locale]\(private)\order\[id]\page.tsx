'use client';
import {apiClient } from "@/utils/apiClient";
import { useLocale, useTranslations} from 'next-intl';
import { useCallback, useEffect, useState } from "react";
import * as React from 'react';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import CheckoutDetails from "../checkout-details/checkoutDetails";
import OrderSummary from "../order-summary/orderSummary";
import { RootState, AppDispatch } from '@/redux/stores';
import { useSelector, useDispatch } from 'react-redux';
import {IOrderItem} from "@/interface/IOrderItem";
import { Button, CircularProgress, debounce, IconButton, Modal, Typography } from "@mui/material";
import LoadingBackdrop from '@/components/LoadingBackdrop';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import { useRouter } from 'next/navigation';


interface Props{
    params:{
        id:string;
    }
}

const greenButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'white',
  backgroundColor: 'rgba(91, 170, 100, 1)',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(91, 170, 100, 1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
  alignItems: 'center', 
  // width: '100%', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }  
}

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 4,
  paddingTop:"5px",
  paddingRight:"20px",
  borderRadius:"24px",
  outline: 'none'
  // backgroundColor:"rgba(0, 0, 0, 0.5)"
};

const whiteButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'black',
  backgroundColor: 'transparent',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(230, 232, 236, 0.1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
  display: 'flex', 
  justifyContent: 'space-between', 
  alignItems: 'center', 
  // width: 'auto', 
  // minWidth: '140px', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }
}

const OrderId = ({params}:Props) =>{

    const router = useRouter();
    const dispatch = useDispatch<AppDispatch>();
    const {userData, loading, error} = useSelector((state: RootState)=> state.userData);
    const [orderItem, setOrderItem] = useState<IOrderItem | undefined>(undefined);
    const [shippingType,setShippingType] = useState<string>("storePickup");
    const [selectedPayment,setSelectedPayment] = useState({
      paymentCode: '',
      paymentPlatform: '',
      paymentType: ''
    });
    const [orderContact,setOrderContact] = useState({
      name:"",
      countryCode:"",
      tel:"",
      email:""
    });
    const [orderBilling,setOrderBilling] = useState({
      name:"",
      countryCode:"",
      tel:"",
      address:""
    })
    const [orderShipping,setOrderShipping] = useState({
      name:"",
      countryCode:"",
      tel:"",
      address:""
    })
    const [idempotencyKeyDto,setIdempotencyKeyDto] = useState<any>(null);
    const [loadingBackdropOpen,setLoadingBackdropOpen] = useState<boolean>(false);
    const [isAgreeTerms,setIsAgreeTerms] = useState<boolean>(false);
    const currentLocale = useLocale();
        const localeToLanguageCode: Record<string, string> = {
        en: 'EN',
        zh: 'TC',
        th: 'TH'
    };  
    const languageCode = localeToLanguageCode[currentLocale] || 'EN';
    const [sectionIds, setSectionIds] = useState<{ [key: string]: any}>({});
    const [valueTab,setValueTab] = useState(0);
    const [openModal, setOpenModal] = useState(false);
    const handleModalOpen = () => setOpenModal(true);
    const handleModalClose = () => setOpenModal(false);
    const [open, setOpen] = React.useState(false);
    const handleClose = () => setOpen(false);
    const [addToCartError,setAddToCartError] = useState<boolean>(false);
    const [orderCompleted,setOrderCompleted] = useState<boolean>(false);
    const t = useTranslations("order_page");
    // const handleOpen = () => setOpen(true);
    const handleSectionIds = useCallback((ids: { [key: string]: string }) => {
    setSectionIds((prev) => ({ ...prev, ...ids }));
    }, []);

      const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setValueTab(newValue);
    switch (newValue) {
      case 0:
        document.getElementById(sectionIds.payerInfo)?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 1:
        document.getElementById(sectionIds.shippingMethod)?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 2:
        document.getElementById(sectionIds.paymentMethod)?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 3:
        document.getElementById(sectionIds.orderSummary)?.scrollIntoView({ behavior: 'smooth' });
        break;
    }
  };

    const handleShippingType = (shippingType:string) => {
        setShippingType(shippingType);
    }

    const handleSelectedPayment = (selectedPayment:any) =>{
      setSelectedPayment(selectedPayment);
    }

    const handleOrderContact = (orderContact:any) =>{
      setOrderContact(orderContact);
    }

    const handleOrderBilling = (orderBilling:any) =>{
      setOrderBilling(orderBilling);
    }

    const handleOrderShipping = (orderShipping:any) =>{
      setOrderShipping(orderShipping);
    }

    const handleAgreeTermsChange = (isAgreeTerms:boolean) =>{
      setIsAgreeTerms(isAgreeTerms);
    }
    

    const postExpiredOrderToCart = async () =>{

      const parsedToken = getParsedUserToken();

        if (!parsedToken) {
            console.log('No user token found');
            return;
        }
      
      try{

          const orderId = Number(params.id);
          const bodyData = {
            orderId:orderId
          }

          const response = await apiClient.post(`/order/expired-order-to-cart`,parsedToken?.token,bodyData);

          if(response.success){
            return response.success;
          }else if(!response.success){
            console.log("add expired order to cart failed",response)
          }

      }catch(error){

        console.error('add expired order to cart error', error);   

      }

    }

    const handleRetureToHome = () =>{
      router.push(`/${currentLocale}/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}`);
    }

    const handleReturnToCart = async () =>{
      // router.push(`/${currentLocale}/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}?cart=open`);
      setLoadingBackdropOpen(true);
      const success = await postExpiredOrderToCart();

      if(success){
        window.location.href = `/${currentLocale}/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}?cart=open`;
      }else{
        console.log("failed to add expired order to cart")
        setLoadingBackdropOpen(false);
        setAddToCartError(true);
      }

    }

    const getParsedUserToken = () => {
        const userIdToken = localStorage.getItem('userIdToken');
        
        if (!userIdToken) {
        console.log('No user token found');
        return null;
        }
        
        try {
        return JSON.parse(userIdToken);
        } catch (error) {
        console.error('Error parsing user token:', error);
        return null;
        }
    };

    const getOrderItems = async () =>{

        try{

          const parsedToken = getParsedUserToken();

          if (!parsedToken) {
              console.log('No user token found');
              return;
          }

          try{

            const data = await apiClient.get(`/order/${params.id}?language=${languageCode}`, parsedToken?.token)

            if(data?.success){
                console.log('get order item successful', data);
                setOrderItem(data);
            }

          }catch(error){

              console.error('Error fetching order items:', error);

          }

        }catch(error){

          console.error('Error processing get order', error);

        }
    }
    // console.log("check out idempotencyKeyDto",idempotencyKeyDto)
    const postGetIdempotencyKey = async () =>{

      const parsedToken = getParsedUserToken();

      if (!parsedToken) {
          console.log('No user token found');
          return;
      }

      try{
          const orderId = Number(params.id);
          const paymentData = {
            orderId:orderId,
            shippingType:shippingType,
            paymentMethod:selectedPayment?.paymentCode,
            paymentPlatform:selectedPayment?.paymentPlatform,
            paymentType:selectedPayment?.paymentType,
            orderContact:orderContact,
            orderBilling:orderBilling
          }

          const data = await apiClient.post(`/order/payment/idempotency-key`,parsedToken?.token,paymentData);

          if(data.success){
            setIdempotencyKeyDto(data);
            console.log('post idempotency key success',data);
            return data.data?.token; 
          }else if(!data.success){
          console.log("post failed",data)
          }

      }catch(error){

        console.error('post idempotency key error:', error);
        throw error;

      }
    }

    const token = idempotencyKeyDto?.data?.token;
    // console.log("idempotencyKeyToken token",token);

    const postShippingData = async () =>{

        try{

          const parsedToken = getParsedUserToken();

          if (!parsedToken) {
              console.log('No user token found');
              return;
          }

          try{

            const orderId = Number(params.id);

            const shippingData = {
                orderId:orderId,
                shippingType:shippingType,
                orderShipping:orderShipping
            }

            const data = await apiClient.post(`/order/shipping`,parsedToken?.token, shippingData);

            if(data.success){

              console.log('Shipping type updated successfully');
          
              await getOrderItems();

              return data?.success;

            }else if(!data.success){

               console.log('post shipping data failed',data);
               
            }

          }catch(error){

            console.error('Error fetching order items:', error);

          }

        }catch(error){
          
          console.error('Error processing post shipping data api', error);

        }
    }

    const handleCheckOutOrder = async (token:string) =>{

        try{

          const parsedToken = getParsedUserToken();

          if (!parsedToken) {
              console.log('No user token found');
              return;
          }
          const orderId = Number(params.id);
          const paymentData = {
            orderId:orderId,
            shippingType:shippingType,
            paymentMethod:selectedPayment?.paymentCode,
            paymentPlatform:selectedPayment?.paymentPlatform,
            paymentType:selectedPayment?.paymentType,
            orderContact:orderContact,
            orderBilling:orderBilling
          }

          const data = await apiClient.post(
            `/order/payment?language=${languageCode}`,
            parsedToken?.token,
            paymentData,
            {"X-Incutix-Idempotency-Key":token}
          )

          if(data.success){

              console.log("Payment Api return",data);
              
              const success = await postShippingData();

              if(success){

                window.location.href = data.data?.url;

              }

          }else if(!data.success){

            console.log("Post checkout failed",data);
            setOpenModal(true)
            setLoadingBackdropOpen(false);
            
          }

        }catch(error){
          console.error('checkout failed', error);
        }
    }

    const handlePaymentCheckOutButton = async() =>{
      try{

        const token = await postGetIdempotencyKey();
        if (!token) throw new Error("Failed to get token");
        setLoadingBackdropOpen(true)
        await handleCheckOutOrder(token); 
      }catch(error){
        console.error("Checkout failed:", error); 
      }
    }

    useEffect(()=>{
        getOrderItems();
    },[]);

    useEffect(()=>{
        if (shippingType) { 
          postShippingData();
        }
    },[shippingType]);

    useEffect(()=>{
      if(orderItem?.data?.orderStatus.includes('cancel')){
        setOpen(true);
      }else if(orderItem?.data?.orderStatus.includes('complete')){
        setOpen(true);
        setOrderCompleted(true);
      }else if(orderItem?.data?.orderStatus.includes('inProgress')){
        setOpen(false);
      }
    },[orderItem?.data?.orderStatus]);

    useEffect(() => {
      if (orderItem?.data?.paymentMethod?.[0]) {
        setSelectedPayment({
          paymentCode: orderItem.data.paymentMethod[0].paymentCode,
          paymentPlatform: orderItem.data.paymentMethod[0].paymentPlatform,
          paymentType: orderItem.data.paymentMethod[0].paymentType
        });
      }
    }, [orderItem]);

    return(
        <>
            <Box sx={{ flexGrow: 1, p: { xs: 2, md: 4 } }}>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={8}>
                      {
                        orderItem ?
                        <CheckoutDetails 
                        userData={userData} 
                        orderItem={orderItem}
                        handleShippingType={handleShippingType}
                        handleSelectedPayment={handleSelectedPayment}
                        handleOrderContact={handleOrderContact}
                        handleOrderBilling={handleOrderBilling}
                        handleOrderShipping={handleOrderShipping}
                        handleSectionIds={handleSectionIds}
                        />:
                        <Grid container justifyContent="center" alignItems="center" 
                        sx={{
                          height:"50vh",
                          marginRight:{md:"400px"}
                        }}
                        >
                          <CircularProgress sx={{color:"#ff7802"}}/>
                        </Grid>
                      }
                    </Grid>
                    <Grid item xs={12} md={4}>
                      {
                          orderItem ?
                          <>
                            <OrderSummary 
                            orderItem={orderItem}
                            handleAgreeTermsChange={handleAgreeTermsChange}
                            handleSectionIds={handleSectionIds}
                            />
                            <Button 
                            sx={greenButtonCss} 
                            onClick={handlePaymentCheckOutButton}
                            disabled={!isAgreeTerms}
                            >{t('go_to_payment')}</Button>
                          </>:
                          <Grid container justifyContent="center" alignItems="center" 
                            sx={{
                              height:"50vh",
                              marginRight:{md:"400px"}
                            }}
                            >
                              <CircularProgress sx={{color:"#ff7802"}}/>
                            </Grid>
                      }
                    </Grid>
                </Grid>
                <LoadingBackdrop open={loadingBackdropOpen}/>
            </Box>
            <Modal
                open={openModal}
                onClose={handleModalClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                sx={{backgroundColor:"rgba(0, 0, 0, 0.5)"}}
              > 
                <Box sx={modalStyle}>
                  <Box sx={{display:"flex",justifyContent:"flex-end"}}>
                    <IconButton onClick={handleModalClose}>
                    <CancelOutlinedIcon/>
                    </IconButton>
                  </Box>
                  <Box sx={{display:"flex",justifyContent:"center"}}>
                  <Typography id="modal-modal-title" variant="h2" component="h2">
                      {t('error')} !
                  </Typography>
                  </Box>
                  <Box sx={{display:"flex",justifyContent:"space-around",mt:2}}>
                    <Button sx={whiteButtonCss} onClick={handleModalClose}>{t('cancel')}</Button>
                  </Box>
                </Box>
            </Modal>
            <Modal
                open={open}
                onClose={()=>setAddToCartError(false)}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                sx={{backgroundColor:"rgba(0, 0, 0, 0.5)"}}
              >
                <Box 
                sx={modalStyle}
                >
                  {/* <Typography id="modal-modal-title" variant="h6" component="h2">
                    Text in a modal
                  </Typography>
                  <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                    Duis mollis, est non commodo luctus, nisi erat porttitor ligula.
                  </Typography> */}
                      <Box sx={{display:"flex",justifyContent:"flex-end"}}>
                        {/* <IconButton onClick={handleConstructionClose}>
                        <CancelOutlinedIcon/>
                        </IconButton> */}
                      </Box>
                      <Box sx={{display:"flex",justifyContent:"center"}}>
                          {/* {renderModalMessage()} */}
                        <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                          {orderCompleted ? `${t('payment_completed')}` : `${t('payment_time_limit')} ${t('expired')}`}
                        </Typography>
                      </Box>
                        <Box sx={{display:"flex",justifyContent:"space-around",mt:2}}>
                          <Button 
                          sx={{
                            ...whiteButtonCss,
                            padding:"5px 10px"
                          }}
                          onClick={handleRetureToHome}
                          >
                            <Typography>
                              {t('returnHome')}
                            </Typography>
                          </Button>
                          {
                            orderCompleted ? "":(
                              <Button 
                              sx={{
                                ...greenButtonCss,
                                padding:"5px 10px"
                              }} 
                              onClick={handleReturnToCart}
                              >
                                {t('returnCart')}
                              </Button>
                            )
                          }
                        </Box>
                        <Box sx={{display:"flex",justifyContent:"center"}}>
                          {
                            addToCartError &&
                            <Typography 
                            sx={{
                              fontSize:"16px",
                              mt:1,
                              color:"rgba(245, 34, 45, 1)"
                            }}>*{t('unable_to_add_expired_order')}</Typography>
                          }
                        </Box>  
                     <LoadingBackdrop open={loadingBackdropOpen}/>
                </Box>
            </Modal>  
        </>
    )
}

export default OrderId;