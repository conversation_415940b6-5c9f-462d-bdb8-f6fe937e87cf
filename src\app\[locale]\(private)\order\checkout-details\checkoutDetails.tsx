import { Box, FormControlLabel, TextField, Theme, Typography, useMediaQuery } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import Checkbox from '@mui/material/Checkbox';
import PaymentMethod from './paymentMethod';
import { IOrderItem } from "@/interface/IOrderItem";
import './style.css';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

const titleStyle = {
  // color: "rgba(91, 170, 100, 1)",
  color:"rgba(0, 0, 0, 1)",
  mb: 4
}

const textDecoration = {
  borderLeft: "5px solid rgba(91, 170, 100, 1)",
  borderRadius: "4px",
  paddingLeft: "10px",
  mt: 5
}

const checkboxStyle = {
    '& .MuiSvgIcon-root': {
    borderRadius: '50%',
  },
  '&.Mui-checked': {
  color: 'rgba(91, 170, 100, 1)',
  },
}

const textFieldStyle = {
  '& .MuiOutlinedInput-root': {
    '&:hover fieldset': {
      borderColor: 'rgba(79, 183, 71, 1)',
      borderRadius: '999px'
    },
    '&.Mui-focused fieldset': {
      borderColor: 'rgba(79, 183, 71, 1)',
      borderRadius: '999px'
    },
    '& fieldset': {
      borderRadius: '999px', 
    },
    '& input::placeholder': { 
      fontSize: '14px',
    },
  },
  '& .MuiInputLabel-root': {  
    fontSize: '14px',
      '&.MuiInputLabel-shrink': {
        transform: 'translate(14px, -9px) scale(0.75)',
        backgroundColor: 'white', // Prevents label background from overlapping border
        padding: '0 4px',
      },        
    '&.Mui-focused': {        //
      color: 'rgba(79, 183, 71, 1)',
    },
  },
  mt: 1
}


type Props = {
  userData: any;
  orderItem: IOrderItem,
  handleShippingType: (shippingType: string) => void;
  handleSelectedPayment: (selectedPayment: any) => void;
  handleOrderContact: (orderContact: any) => void;
  handleOrderBilling: (orderBilling: any) => void;
  handleOrderShipping: (orderShipping: any) => void;
  handleSectionIds: (ids: { [key: string]: any}) => void;
  // sectionIds:any;
}

const CheckoutDetails = ({ userData, orderItem,
  handleShippingType,
  handleSelectedPayment,
  handleOrderContact,
  handleOrderBilling,
  handleOrderShipping,
  handleSectionIds,
  // sectionIds
}: Props) => {

  const t = useTranslations("order_page");
  const [phone, setPhone] = useState('');
  const [useAccountData, setUseAccountData] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    countryCode: "",
    tel: "",
    email: ""
  });
  const [useBillingData, setUseBillingData] = useState(false);
  const [formBillingData, setFormBillingData] = useState({
    name: "",
    countryCode: "",
    tel: "",
    address: ""
  });
  const [formShippingData, setFormShippingData] = useState({
    name: "",
    countryCode: "",
    tel: "",
    address: ""
  })
  // const [expanded, setExpanded] = useState(false);
  const [pickupMethod, setPickupMethod] = useState<'storePickup' | 'express' | null>('storePickup');
  const [phoneNumberInput, setPhoneNumberInput] = useState('');
  const [billingPhoneNumber, setBillingPhoneNumber] = useState('');
  const [shoppingPhoneNumber, setShoppingPhoneNumber] = useState('');
  const [valueTab,setValueTab] = useState(0);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) =>{
    setValueTab(newValue);
      switch(newValue) {
    case 0:
      document.getElementById('payer-info')?.scrollIntoView({ behavior: 'smooth' });
      break;
    case 1:
      document.getElementById('shipping-method')?.scrollIntoView({ behavior: 'smooth' });
      break;
    case 2:
      document.getElementById('payment-method')?.scrollIntoView({ behavior: 'smooth' });
      break;
    case 3:
      document.getElementById('order-summary')?.scrollIntoView({ behavior: 'smooth' });
       break;
  }
  }

    const sectionIds = {
    payerInfo: 'payer-info',
    shippingMethod: 'shipping-method',
    paymentMethod: 'payment-method',
    // orderSummary: 'order-summary',
    };

    useEffect(() => {
      handleSectionIds(sectionIds);
    }, []);

  console.log("formData", formData)
  console.log("pickupMethod", pickupMethod)
  // console.log("phoneNumberInput",phoneNumberInput)

  const handleCheckBillingboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setUseBillingData(isChecked);

    if (isChecked) {
      setFormBillingData({
        name: userData?.nickName || '',
        countryCode: "",
        address: '',
        tel: ''
      });
    } else {
      setFormBillingData({
        name: '',
        countryCode: "",
        address: '',
        tel: ''
      });
    }
  };
  console.log("formBillingData", formBillingData)

  const handlePhoneNumberInput = (value: string) => {
    setPhoneNumberInput(value);
    setFormData(prev => ({
      ...prev,
      tel: value || ""
    }));
  }

  const handleBillingPhoneNumber = (value: string) => {
    setBillingPhoneNumber(value);
    setFormBillingData(prev => ({
      ...prev,
      tel: value || ""
    }));
  }

  const handleShippingPhoneNumber = (value: string) =>{
    setShoppingPhoneNumber(value);
    setFormShippingData(prev => ({
      ...prev,
      tel: value || ""
    }));
  }

  useEffect(() => {
    handleOrderContact(formData);
    handleOrderBilling(formBillingData);
    handleOrderShipping(formShippingData);
  }, [formData, formBillingData,formShippingData]);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = event.target.checked;
    setUseAccountData(isChecked);

    if (isChecked) {
      setFormData({
        name: userData?.nickName || '',
        email: userData?.email || '',
        tel: '',
        countryCode: "",
      });
    } else {
      setFormData({
        name: '',
        email: '',
        tel: '',
        countryCode: "",
      });
    }
  };

  const handlePickupMethodChange = (method: 'storePickup' | 'express') => {
    if (pickupMethod === method) {

      setPickupMethod(null);
    } else {

      setPickupMethod(method);
      handleShippingType(method);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBillingInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormBillingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleShippingInputChange = (event:any) =>{
    const {name, value} = event.target;
    setFormShippingData(prev => ({
      ...prev,
      [name]: value
    }));
  }

  return (
    <>
      <Typography variant="h1">{t('checkout')}</Typography>
      <Box sx={{ 
        width: '100%', 
        bgcolor: 'background.paper',
        display:"flex",
        justifyContent:"flex-start",
        mt:2
        }}>
      <Tabs 
      value={valueTab} 
      onChange={handleTabChange} 
      // centered
      sx={{
      '& .MuiTab-root': {
        color: 'rgba(91, 170, 100, 1)', // 設置所有標籤的字體顏色
        '&.Mui-selected': {
          color: 'rgba(91, 170, 100, 1)', // 設置選中標籤的字體顏色
        },
      },
      '& .MuiTabs-indicator': {
        backgroundColor: 'rgba(91, 170, 100, 1)', // 設置下劃線顏色
      },
  }}
      >
        <Tab label={t('label_payer_info')} href="#payer-info"/>
        <Tab label={t('label_shipping_method')} href="#shipping-method"/>
        <Tab label={t('label_payment_method')} href="#payment-method"/>
        {isMobile ? <Tab label={t('label_order_summary')}  href="#order-summary"/> : ""}
      </Tabs>
    </Box>
      {/* <p>{userData?.email}</p>
            <p>{userData?.nickName}</p> */}
      <Box sx={textDecoration} id="payer-info">
        <Typography variant="h2" sx={titleStyle}>{t('label_payer_info')}</Typography>
      </Box>
      <Box sx={{
        display: 'flex'
      }}>
        <Box sx={{
          display: 'flex',
          flexDirection: "column",
          //   minWidth: 120,
          width: "100%",
          gap: 2

        }}>
          <TextField
            id="outlined-multiline-static"
            label={t('label_name')}
            placeholder={t('label_enter_name')}
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            InputLabelProps={{
              shrink: true,
            }}
            // inputProps={{
            //   placeholder: "請輸入名稱",  
            //   style: { fontSize:"14px" }, 
            // }}
            sx={textFieldStyle}
          />
          <TextField
            id="outlined-multiline-static"
            label={t('label_email')}
            name="email"
            placeholder={t('label_enter_email')}
            value={formData.email}
            onChange={handleInputChange}
            InputLabelProps={{
              shrink: true,
            }}
            sx={textFieldStyle}
          />
          <PhoneInput
            defaultCountry="hk"
            value={formData.tel}
            onChange={handlePhoneNumberInput}
            className="custom-phone-input"
            name="聯絡電話"
          />
          <Box >
            <FormControlLabel
              control={
                <Checkbox
                  checked={useAccountData}
                  onChange={handleCheckboxChange}
                  sx={checkboxStyle}
                />
              }
              label={t('useAccountInfo')}
            />
          </Box>
          <Box>
            <Typography variant="h2" id="shipping-method">{t('label_shipping_method')}</Typography>
          </Box>
          <Box>
            <Box
              sx={{
                border: 2,
                borderColor: pickupMethod === 'storePickup' ? 'rgba(91, 170, 100, 1)' : 'grey.300',
                borderRadius: "12px",
                paddingTop:"5px",
                paddingBottom:"5px",
                paddingRight:"5px",
                paddingLeft:"20px",
                '&:hover': {
                  borderColor: pickupMethod === 'storePickup' ? 'rgba(91, 170, 100, 1)' : 'grey.500',
                },
                mb:2
              }}
            >
            <FormControlLabel
              control={
                <Checkbox
                  checked={pickupMethod === 'storePickup'}
                  onChange={() => handlePickupMethodChange('storePickup')}
                  sx={checkboxStyle}
                />
              }
              label={t('store_pickup')}
            />
            </Box>
            {pickupMethod === 'storePickup' && (
              <>
                <Typography sx={{ fontSize: "14px" }}>{t('pickupTime')}: 2024年6月20日 - 2026年12月31日內*，星期一至日及公眾假期 9:00-18:00</Typography>
                <Typography sx={{ fontSize: "14px" }}>{t('pickupLocation')}: 香港尖沙咀廣東道30號Anima Tokyo 1樓 INCUBASE Arena</Typography>
                <Typography sx={{ fontSize: "14px" }}>{t('not_be_reserved_or_refunded')}</Typography>
              </>
            )}
          </Box>
          <Box>
            <Box 
               sx={{
                border: 2,
                borderColor: pickupMethod === 'express' ? 'rgba(91, 170, 100, 1)' : 'grey.300',
                borderRadius: "12px",
                paddingTop:"5px",
                paddingBottom:"5px",
                paddingRight:"5px",
                paddingLeft:"20px",
                '&:hover': {
                  borderColor: pickupMethod === 'express' ? 'rgba(91, 170, 100, 1)' : 'grey.500',
                },
                mb:2
              }}
            >
            <FormControlLabel
              control={
                <Checkbox
                  checked={pickupMethod === 'express'}
                  onChange={() => handlePickupMethodChange('express')}
                  sx={checkboxStyle}
                />
              }
              label={t('shipping_address')}
              style={{ cursor: 'pointer' }}
            />
            </Box>
            {pickupMethod === 'express' && (
              <>
                <Box sx={{ display: 'flex' }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: "column",
                    width: "100%",
                    gap: 2
                  }}>
                    <TextField
                      id="outlined-multiline-static"
                      label={t('label_recipient_name')}
                      placeholder={t('label_enter_recipient_name')}
                      name="name"
                      value={formShippingData.name}
                      onChange={handleShippingInputChange}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      sx={textFieldStyle}
                    />
                    <PhoneInput
                      defaultCountry="hk"
                      value={formShippingData.tel}
                      onChange={handleShippingPhoneNumber}

                    />
                    <TextField
                      id="outlined-multiline-static"
                      label={t('label_receiving_address')}
                      placeholder={t('label_enter_receiving_address')}
                      name="address"
                      value={formShippingData.address}
                      onChange={handleShippingInputChange}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      sx={textFieldStyle}
                    />
                  </Box>
                </Box>
              </>
            )}
          </Box>
        </Box>
      </Box>
      <Box sx={textDecoration}>
        <Typography variant="h2" sx={titleStyle} id="payment-method">{t('label_payment_method')}</Typography>
      </Box>
      <Box>
        <Typography sx={{ fontSize: "18px" }}>{t('select_payment_method')}</Typography>
      </Box>
      <Box>
        {
          <PaymentMethod
            orderItem={orderItem}
            handleSelectedPayment={handleSelectedPayment}
          />
        }
      </Box>
      <Box>
        <Typography sx={{ fontSize: "18px" }}>{t('billing_address')}</Typography>
      </Box>
      <Box sx={{ display: 'flex' }}>
        <Box sx={{
          display: 'flex',
          flexDirection: "column",
          width: "100%",
          gap: 2,
          mt: 2
        }}>
          <TextField
            id="outlined-multiline-static"
            label={t('label_name')}
            name="name"
            placeholder={t('label_enter_name')}
            value={formBillingData.name}
            onChange={handleBillingInputChange}
            InputLabelProps={{
              shrink: true,
            }}
            sx={textFieldStyle}
          />
          <PhoneInput
            defaultCountry="hk"
            value={formBillingData.tel}
            onChange={handleBillingPhoneNumber}
          />
          <TextField
            id="outlined-multiline-static"
            label={t('billing_address')}
            placeholder={t('label_billing_address')}
            name="address"
            value={formBillingData.address}
            onChange={handleBillingInputChange}
            InputLabelProps={{
              shrink: true,
            }}
            sx={textFieldStyle}
          />
        </Box>
      </Box>
    </>
  )
}

export default CheckoutDetails;