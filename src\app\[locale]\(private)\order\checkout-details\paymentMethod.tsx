import { Box, Typography } from '@mui/material';
import * as React from 'react';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { IOrderItem } from "@/interface/IOrderItem";
import { useState } from 'react';

type Props = {
    orderItem: IOrderItem;
    handleSelectedPayment:(selectedPayment:any)=> void;
}

const paymentDisplayNames: Record<string, string> = {
    'prompt_pay': 'Prompt Pay',
    'truemoney': 'TrueMoney',
    'online_banking': 'Thai online banking',
    'card': 'Credit Card',
    'alipayhk': 'Alipay HK',
    'fps': 'FPS',
    'wechatpay': 'Wechat Pay'
};

const paymentImageCss = {
    width:"52px",
    height:"36px"
}

const paymentDisplayImage: Record<string, React.ReactNode> = {
    'prompt_pay': <img src={"/images/IncutixPromptPay.png"} style={paymentImageCss} alt="PromptPay" />,
    'truemoney': <img src={"/images/IncutixTrueMoney.png"} style={paymentImageCss} alt="TrueMoney" />,
    'online_banking': "",
    'card': [
        <img key="1" src={"/images/IncutixVisa.png"} style={paymentImageCss} alt="Visa" />,
        <img key="2" src={"/images/IncutixMaster.png"} style={paymentImageCss} alt="Master" />,
        <img key="3" src={"/images/IncutixAE.png"} style={paymentImageCss} alt="AE" />,
        <img key="4" src={"/images/IncutixUnion.png"} style={paymentImageCss} alt="Union" />
    ],
    'alipayhk': <img src={"/images/IncutixAlipayHK.png"} style={paymentImageCss} alt="Alipay HK" />,
    'fps': <img src={"/images/IncutixFPS.png"} style={paymentImageCss} alt="FPS" />,
    'wechatpay': <img src={"/images/IncutixWechatPay.png"} style={paymentImageCss} alt="Wechat Pay" />
};


const PaymentMethod = ({ orderItem,handleSelectedPayment }: Props) => {

    const paymentMethods = orderItem?.data?.paymentMethod || [];
    const [selectedPaymentCode, setSelectedPaymentCode] = useState(
        paymentMethods[0]?.paymentCode || ''
    );

    // console.log("selectedPaymentCode",selectedPaymentCode?.paymentCode)

    const selectedPayment = paymentMethods.find(payment => payment.paymentCode === selectedPaymentCode);

    // console.log("selectedPayment", selectedPayment)
    // console.log("selectedPayment", selectedPayment?.paymentPlatform)

    const handlePaymentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const code = event.target.value;
        setSelectedPaymentCode(code);

        // const selectedPayment = paymentMethods.find(payment => payment.paymentCode === code);
        const selectedPayment = paymentMethods.find(payment => payment.paymentCode === code);
        handleSelectedPayment(selectedPayment)
    };
    

    return (
        <>
            <Box sx={{ mt: 2, mb: 2 }}>
                <FormControl fullWidth>
                    <RadioGroup
                        aria-labelledby="demo-radio-buttons-group-label"
                        defaultValue={paymentMethods?.[0]?.paymentCode}
                        value={selectedPaymentCode}
                        onChange={handlePaymentChange}
                        name="radio-buttons-group"
                    >
                        {
                            paymentMethods.map((value) => {

                                const displayName = paymentDisplayNames[value.paymentCode] || value.paymentCode;
                                const displayImage = paymentDisplayImage[value.paymentCode] || null;
                                return (
                                    <>
                                    <Box               
                                    sx={{
                                    border: 2,
                                    borderColor: selectedPaymentCode === value.paymentCode ? 'rgba(91, 170, 100, 1)' : 'grey.300',
                                    borderRadius: "12px",
                                    paddingTop:"5px",
                                    paddingBottom:"5px",
                                    paddingRight:"20px",
                                    paddingLeft:"20px",
                                    '&:hover': {
                                      borderColor: selectedPaymentCode === value.paymentCode ? 'rgba(91, 170, 100, 1)' : 'grey.500',
                                    },
                                    mb:2,
                                    }}>
                                        <Box sx={{
                                            display:"flex",
                                            justifyContent:"space-between"
                                        }}>
                                        <FormControlLabel
                                            key={value.paymentCode}
                                            value={value.paymentCode}
                                            control={<Radio  
                                                sx={{
                                                '&.Mui-checked': {
                                                color: 'rgba(91, 170, 100, 1)',
                                                },
                                            }}/>}
                                            label={displayName}
                                        />
                                        <Box>
                                            {displayImage}
                                        </Box>
                                        </Box>
                                    </Box>
                                    </>
                                )
                            })
                        }
                    </RadioGroup>
                </FormControl>
            </Box>
        </>
    )

}

export default PaymentMethod;