/* .custom-phone-input .PhoneInputCountrySelect {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.custom-phone-input .PhoneInputCountryIconImg {
  width: 20px;
  height: 15px;
} */

.react-international-phone-input-container .react-international-phone-country-selector-button {
  border-top-left-radius: 999px;
  border-bottom-left-radius: 999px;
  padding: 10px;
  height: 45px;
}

.react-international-phone-input-container .react-international-phone-input{
  border-top-right-radius: 999px;
  border-bottom-right-radius: 999px;
  padding: 10px;
  height: 45px;
}

.react-international-phone-input-container .react-international-phone-input:hover {
  border-color: rgba(91, 170, 100, 1) !important; /* Apply the green color on hover */
}

.react-international-phone-input-container .react-international-phone-input:focus {
  border-color: rgba(91, 170, 100, 1) !important; /* Apply the green color on hover */
}

