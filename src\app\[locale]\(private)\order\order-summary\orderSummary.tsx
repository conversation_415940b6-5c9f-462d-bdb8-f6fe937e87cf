import { Box, Typography, FormControlLabel, styled, useMediaQuery, Theme } from '@mui/material';
import { IOrderItem } from "@/interface/IOrderItem";
import Divider from '@mui/material/Divider';
import Checkbox from '@mui/material/Checkbox';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

const titleStyle = {
    color: "rgba(0, 0, 0, 1)",
    // mb: 2
}

const textDecoration = {
    borderLeft: "5px solid rgba(91, 170, 100, 1)",
    borderRadius: "4px",
    paddingLeft: "10px",
    mt: 5
}

const checkboxStyle = {
    '& .MuiSvgIcon-root': {
        borderRadius: '50%',
    },
    '&.Mui-checked': {
        color: 'rgba(91, 170, 100, 1)',
    },
}

const StickyContainer = styled('div')(({ theme }) => ({
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    display: 'flex',
    justifyContent: 'space-between',
    padding: theme.spacing(2),
    backgroundColor: 'white',
    gap: 10,
    boxShadow: '0px -2px 10px rgba(0, 0, 0, 0.1)',
    zIndex: 1000,
    [theme.breakpoints.up('md')]: {
        display: 'none',
    },
}));

const StickyCountDown = styled('div')(({ theme }) => ({
    position: 'fixed',
    // top: theme.spacing(2),
    top:"70px",
    right: theme.spacing(2),
    display: 'flex',
    justifyContent: 'flex-end', 
    borderRadius:"8px",
    backgroundColor: 'white',
    gap: 10,
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)', 
    zIndex: 1000,
    [theme.breakpoints.up('md')]: {
        display: 'none',
    },
})); 


type Props = {
    orderItem: IOrderItem;
    handleAgreeTermsChange: (isAgreeTerms: boolean) => void;
    handleSectionIds: (ids: { [key: string]: any }) => void;
}

const OrderSummary = ({
    orderItem,
    handleAgreeTermsChange,
    handleSectionIds
}: Props) => {
    const t = useTranslations("order_page");
    const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
    const [isSummaryVisible, setIsSummaryVisible] = useState(false);
    // const [timeLeft, setTimeLeft] = useState<{ minutes: number, seconds: number }>({ 
    //     minutes: 10, 
    //     seconds: 0 
    // });
    const [isTimeUp, setIsTimeUp] = useState(false);

    const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked;

        if (isChecked) {
            handleAgreeTermsChange(true);
        } else {
            handleAgreeTermsChange(false);
        }
    }

    const sectionIds = {
        orderSummary: 'order-summary',
    };


    useEffect(() => {
        handleSectionIds(sectionIds);

        if (isMobile) {
            const handleScroll = () => {
                const summaryElement = document.getElementById('order-summary');
                if (summaryElement) {
                    const rect = summaryElement.getBoundingClientRect();

                    setIsSummaryVisible(rect.top <= window.innerHeight && rect.bottom >= 0);
                }
            };

            window.addEventListener('scroll', handleScroll);
            return () => window.removeEventListener('scroll', handleScroll);
        }
    }, [isMobile]);

            const expireAt = orderItem?.data?.orderExpireAt;
        const orderStatus = orderItem?.data?.orderStatus;
        
        const calculateTimeLeft = () => {
            if (!expireAt || orderStatus !== 'inProgress') {
                return { minutes: 0, seconds: 0 };
            }
            
            const now = Math.floor(Date.now() / 1000); 
            const remainingSeconds = Math.max(0, expireAt - now);
            
            return {
                minutes: Math.floor(remainingSeconds / 60),
                seconds: remainingSeconds % 60
            };
        };
        
        const [timeLeft, setTimeLeft] = useState(() => calculateTimeLeft());

        useEffect(() => {

            if (orderStatus !== 'inProgress') return;
            
            const timer = setInterval(() => {
                const newTimeLeft = calculateTimeLeft();
                setTimeLeft(newTimeLeft);
                
                if (newTimeLeft.minutes === 0 && newTimeLeft.seconds === 0) {
                    clearInterval(timer);
                }
            }, 1000);
            
            return () => clearInterval(timer);
        }, [expireAt, orderStatus]);

    const renderCountDown = () => {
        
        const getStatusText = () => {
            switch (orderStatus) {
                case 'inProgress':
                    return `${timeLeft.minutes.toString().padStart(2, '0')}:${timeLeft.seconds.toString().padStart(2, '0')}`;
                case 'cancel':
                    return t('expired');
                case 'complete':
                    return t('completed');
                default:
                    return '';
            }
        };
        
        const getStatusColor = () => {
            switch (orderStatus) {
                case 'inProgress':
                    return "rgba(91, 170, 100, 1)"; 
                case 'cancel':
                    return "rgba(255, 0, 0, 1)"; 
                case 'complete':
                    return "rgba(0, 0, 255, 1)";
                default:
                    return "rgba(0, 0, 0, 1)"; 
            }
        };
        
        const countDownBox = (
            <Box sx={{
                display: "flex",
                justifyContent: "space-between",
                border: "2px solid rgba(230, 232, 236, 1)",
                borderRadius: "8px",
                alignItems: "center",
                paddingTop: "3px",
                paddingRight: "12px",
                paddingBottom: "6px",
                paddingLeft: "12px"
            }}>
                <Typography sx={{ fontSize: isMobile ? "13px" : "14px" }}>
                    {t('payment_time_limit')}:&nbsp;
                </Typography>
                <Typography sx={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: getStatusColor()
                }}>
                    {getStatusText()}
                </Typography>
            </Box>
        );
        
        if (isMobile) {
            return (
                <StickyCountDown>
                    {countDownBox}
                </StickyCountDown>
            );
        } else {
            return countDownBox;
        }
    };

    const renderOrderSummaryAmount = () => {
        if (isMobile) {
            return (
                <>
                    {!isSummaryVisible && (
                        <StickyContainer>
                            <Box>
                                <Typography sx={{ fontSize: "16px" }}>
                                    {orderItem?.data?.orderSummary?.orderTotalQuantity}
                                    {t(orderItem?.data?.orderSummary?.orderTotalQuantity === 1 ? 'item' : 'items')}
                                </Typography>
                            </Box>
                            <Box sx={{ display: "flex" }}>
                                <Typography>{t('total')}:</Typography>
                                <Typography sx={{ fontSize: "24px", color: "rgba(91, 170, 100, 1)" }}>
                                    {orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderTotalAmount}
                                </Typography>
                            </Box>
                        </StickyContainer>
                    )}
                    {isSummaryVisible && (
                        <Box sx={{ mt: 2, mb: 2, display: "flex", justifyContent: "space-between" }}>
                            <Box>
                                <Typography sx={{ fontSize: "16px" }}>
                                    {orderItem?.data?.orderSummary?.orderTotalQuantity}
                                    {t(orderItem?.data?.orderSummary?.orderTotalQuantity === 1 ? 'item' : 'items')}
                                </Typography>
                            </Box>
                            <Box sx={{ display: "flex" }}>
                                <Typography>{t('total')}:</Typography>
                                <Typography sx={{ fontSize: "24px", color: "rgba(91, 170, 100, 1)" }}>
                                    {orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderTotalAmount}
                                </Typography>
                            </Box>
                        </Box>
                    )}
                </>
            );
        } else {
            return (
                <Box sx={{ mt: 2, mb: 2, display: "flex", justifyContent: "space-between" }}>
                    <Box>
                        <Typography sx={{ fontSize: "16px" }}>
                            {orderItem?.data?.orderSummary?.orderTotalQuantity}
                            {t(orderItem?.data?.orderSummary?.orderTotalQuantity === 1 ? 'item' : 'items')}
                        </Typography>
                    </Box>
                    <Box sx={{ display: "flex" }}>
                        <Typography>{t('total')}:</Typography>
                        <Typography sx={{ fontSize: "24px", color: "rgba(91, 170, 100, 1)" }}>
                            {orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderTotalAmount}
                        </Typography>
                    </Box>
                </Box>
            );
        }
    }

    return (
        <>
            <Box sx={{
                ...textDecoration,
                display:"flex",
                justifyContent:"space-between",
                alignItems:"center",
                mb:2
                }} 
                id="order-summary">
                <Typography variant="h2" sx={titleStyle}>{t('label_order_summary')}</Typography>
                {renderCountDown()}
            </Box>
            <Box sx={{ ml: 2 }}>
                <Typography sx={{ fontSize: "16px" }}>{orderItem?.data?.events?.[0]?.eventName}</Typography>
                {
                    orderItem?.data?.orderItems.map((value) => {
                        const attributeText = value?.skuAttribute?.map((attr: any) =>
                            `${attr.category.charAt(0).toUpperCase() + attr.category.slice(1)}: ${attr.value}`
                        ).join(', ');
                        return (
                            <>
                                <Box sx={{ display: "flex", gap: 2, mt: 3 }}>
                                    <img src={`${value.thumbnail}`} width="60px" height="60px"></img>
                                    <Box>
                                        <Typography sx={{ fontSize: "14px" }}>{value.productName}</Typography>
                                        <Typography sx={{ fontSize: "12px", color: "rgba(119, 126, 144, 1)", mb: 2 }}>{attributeText || 'No attributes'}</Typography>
                                        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                            <Typography sx={{ fontSize: "14px" }}>{orderItem?.data?.currency} {value.price.unitPrice}</Typography>
                                            <Typography sx={{ fontSize: "14px" }}>x{value.quantity}</Typography>
                                        </Box>
                                    </Box>
                                </Box>
                            </>
                        )
                    })
                }
                <Box sx={{ mt: 2 }}>
                    <Divider />
                </Box>
                <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
                    <Typography sx={{ fontSize: "16px" }}>{t('subtotal')}</Typography>
                    <Typography sx={{ fontSize: "16px" }}>{orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderSubTotal}</Typography>
                </Box>
                <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                    <Typography sx={{ fontSize: "16px" }}>{t('shipping_fee')}</Typography>
                    <Typography sx={{ fontSize: "16px" }}>{orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderShippingFee}</Typography>
                </Box>
                <Box sx={{ mt: 2 }}>
                    <Divider />
                </Box>
                {renderOrderSummaryAmount()}
                <FormControlLabel
                    control={
                        <Checkbox
                            //   checked={useAccountData}
                            onChange={handleCheckboxChange}
                            sx={checkboxStyle}
                        />
                    }
                    label={t('terms_and_conditions')}
                    sx={{
                        alignItems: 'flex-start',
                        '& .MuiFormControlLabel-label': {
                            marginTop: '3px',
                            fontSize: "16px",
                            mb: 2
                        },
                    }}
                />
            </Box>
        </>
    )
}

export default OrderSummary;