import { Box, Typography, FormControlLabel} from '@mui/material';
import {IOrderItem} from "@/interface/IOrderItem";
import Divider from '@mui/material/Divider';
import Checkbox from '@mui/material/Checkbox';
import { useEffect } from 'react';
import { useTranslations } from 'next-intl';

const titleStyle = {
    color:"rgba(0, 0, 0, 1)",
    mb:2
}

const textDecoration = {
    borderLeft: "5px solid rgba(91, 170, 100, 1)",
    borderRadius: "4px",
    paddingLeft: "10px",
    mt:5
}

const checkboxStyle = {
    '& .MuiSvgIcon-root': {
    borderRadius: '50%',
  },
  '&.Mui-checked': {
  color: 'rgba(91, 170, 100, 1)',
  },
}

type Props = {
    orderItem:IOrderItem;
    handleAgreeTermsChange:(isAgreeTerms:boolean)=> void;
    handleSectionIds: (ids: { [key: string]: any }) => void;
}

const OrderSummary = ({
    orderItem,
    handleAgreeTermsChange,
    handleSectionIds
    }:Props) =>{
    const t = useTranslations("order_page");
    const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked;

        if(isChecked){
            handleAgreeTermsChange(true);
        }else {
            handleAgreeTermsChange(false);
        }
    }

    const sectionIds = {
        orderSummary: 'order-summary',
    };

        useEffect(() => {
        handleSectionIds(sectionIds);
    }, []);
 
    return(
        <>
            <Box sx={textDecoration} id="order-summary">
                <Typography variant="h2" sx={titleStyle}>{t('label_order_summary')}</Typography>
            </Box>
            <Box sx={{ml:2}}>
                <Typography sx={{fontSize:"16px"}}>{orderItem?.data?.events?.[0]?.eventName}</Typography>
                {
                    orderItem?.data?.orderItems.map((value)=>{
                    const attributeText = value?.skuAttribute?.map((attr:any) => 
                    `${attr.category.charAt(0).toUpperCase() + attr.category.slice(1)}: ${attr.value}`  
                    ).join(', ');   
                        return(
                            <>
                                <Box sx={{display:"flex",gap:2,mt:3}}>
                                        <img src={`${value.thumbnail}`} width="60px" height="60px"></img>
                                        <Box>
                                            <Typography sx={{fontSize:"14px"}}>{value.productName}</Typography>
                                            <Typography sx={{fontSize:"12px",color:"rgba(119, 126, 144, 1)",mb:2}}>{attributeText || 'No attributes'}</Typography>
                                            <Box sx={{display:"flex",justifyContent:"space-between"}}>
                                                <Typography sx={{fontSize:"14px"}}>{orderItem?.data?.currency} {value.price.unitPrice}</Typography>
                                                <Typography sx={{fontSize:"14px"}}>x{value.quantity}</Typography>
                                            </Box>
                                        </Box>
                                </Box>
                            </>
                        )
                    })
                }
                <Box sx={{mt:2}}>
                    <Divider/>
                </Box> 
                <Box sx={{mt:2,display:"flex",justifyContent:"space-between"}}>
                    <Typography sx={{fontSize:"16px"}}>{t('subtotal')}</Typography>
                    <Typography sx={{fontSize:"16px"}}>{orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderSubTotal}</Typography>
                </Box>
                <Box sx={{display:"flex",justifyContent:"space-between"}}>
                    <Typography sx={{fontSize:"16px"}}>{t('shipping_fee')}</Typography>
                    <Typography sx={{fontSize:"16px"}}>{orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderShippingFee}</Typography>
                </Box>
                <Box sx={{mt:2}}>
                    <Divider/>
                </Box>
                <Box sx={{mt:2,mb:2,display:"flex",justifyContent:"space-between"}}>
                    <Box>
                    <Typography sx={{fontSize:"16px"}}>{orderItem?.data?.orderSummary?.orderTotalQuantity} {t(orderItem?.data?.orderSummary?.orderTotalQuantity === 1 ? 'item' : 'items')}</Typography>
                    </Box>
                    <Box sx={{display:"flex"}}>
                        <Typography>{t('total')}:</Typography><Typography sx={{fontSize:"24px",color:"rgba(91, 170, 100, 1)"}}>{orderItem?.data?.currency} {orderItem?.data?.orderSummary?.orderTotalAmount}</Typography>
                    </Box>
                </Box>
                <FormControlLabel
                    control={
                    <Checkbox
                    //   checked={useAccountData}
                      onChange={handleCheckboxChange}
                      sx={checkboxStyle}
                    />
                    }
                    label={t('terms_and_conditions')}
                      sx={{
                        alignItems: 'flex-start', 
                        '& .MuiFormControlLabel-label': {
                        marginTop: '3px', 
                        fontSize: "16px",
                        mb:2
                        },
                    }}
                />
                </Box>
        </>
    )
}

export default OrderSummary;