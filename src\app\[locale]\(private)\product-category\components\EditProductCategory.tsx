"use client";
import CancelButton from "@/components/buttons/CancelButton";
import EditButton from "@/components/buttons/EditButton";
import SaveButton from "@/components/buttons/SaveButton";
import SelectParentCategory from "@/components/input/SelectParentCategory";
import TextField from "@/components/input/TextField";
import ModalContainer from "@/components/ModalContainer";
import { IProductCategory } from "@/interface/IProductCategory";
import { ProductCategorySchema } from "@/schemas/ProductCategorySchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Modal, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import * as React from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import xior from "xior";
import { z } from "zod";

type FormValue = z.infer<typeof ProductCategorySchema>;

interface Props {
  category: IProductCategory;
}

const EditProductCategory = ({ category }: Props) => {
  const queryClient = useQueryClient();
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors, isDirty },
    setError,
    reset,
    clearErrors,
  } = useForm<FormValue>({
    defaultValues: { name: category.name, parent: category.parent },
    resolver: zodResolver(ProductCategorySchema),
  });

  React.useEffect(() => {
    reset({ name: category.name, parent: category.parent });
  }, [category.name, category.parent, reset]);

  const handleOpen = () => setOpen(true);

  const handleClose = React.useCallback(() => {
    if (isSubmitting) return;
    setOpen(false);
    clearErrors();
    if (isDirty) {
      reset();
    }
  }, [clearErrors, isDirty, isSubmitting, reset]);

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      await xior.put(`/api/product-category/${category.id}`, data);
      queryClient.invalidateQueries({ queryKey: ["productCategory"] });
      reset();
      setOpen(false);
    } catch (e) {
      setError("name", {
        type: "custom",
        message: "duplicated_product_category",
      });
    }
  };

  return (
    <Box>
      <EditButton iconOnly sx={{ marginRight: 1.5 }} onClick={handleOpen} />
      <Modal open={open} onClose={handleClose}>
        <ModalContainer component="form" onSubmit={handleSubmit(onSubmit)}>
          <Box
            borderBottom={"1px solid #777777"}
            alignSelf={"flex-start"}
            paddingBottom={1}
            px={1}
            marginBottom={2}
            marginLeft={-1}
          >
            <Typography fontSize={15} fontWeight={700}>
              {t("product_category.title_edit_product_category")}
            </Typography>
          </Box>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                value={field.value}
                onChange={field.onChange}
                required
                disabled={isSubmitting}
                label={t("product_category.label_name")}
                error={errors?.name?.message}
              />
            )}
          />
          {/* {category.parent && (
            <Controller
              name="parent"
              control={control}
              render={({ field: { onChange, value } }) => (
                <SelectParentCategory
                  value={value}
                  onChange={onChange}
                  label={t("product_category.label_parent_category")}
                  helperText={t(
                    "product_category.label_parent_category_helper_text"
                  )}
                  placeholder="Start typing name to search"
                  disabled={isSubmitting}
                  error={errors?.parent?.message}
                  blacklist={[category]}
                />
              )}
            />
          )} */}
          <Box display={"flex"} flexDirection={"row"} alignSelf={"flex-end"}>
            <CancelButton disabled={isSubmitting} onAction={handleClose} />
            <SaveButton disabled={isSubmitting} />
          </Box>
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default EditProductCategory;
