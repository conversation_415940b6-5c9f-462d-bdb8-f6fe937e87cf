"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  SortingState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Button, Typography, Checkbox } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { useRouter } from "next/navigation";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { format } from "date-fns";
import { IProduct } from "@/interface/IProduct";
import DeleteProduct from "./DeleteProduct";
import EditButton from "@/components/buttons/EditButton";
import { ROUTES } from "@/utils/constants";

const ProductTable = () => {
  const t = useTranslations("");
  const router = useRouter();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data,
    refetch,
    isLoading
  } = useQuery({
    queryKey: ["product", { pagination, sorting }],
    queryFn: async () =>
      xior
        .get<IListResponse<IProduct>>("/api/product", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<IProduct>[]>(
    () => [
      {
        id: 'select-col',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        ),
      },
      {
        accessorKey: "id",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            gap={1}
            mx={1}
            key={`member-list-${data.row.original.userId}-actions`}
          >
            <EditButton
              iconOnly={true}
              onClick={() => router.push(`${ROUTES.PRODUCT}/${data.row.original.id}/edit`)}
            />
            <DeleteProduct product={data.row.original} updateContents={refetch} />
          </Box>
        ),
      },
      {
        accessorKey: "thumbnail",
        header: t("product.label_thumbnail"),
        cell: (data) => {
          const value = data.getValue() as string;
          return (
            data.getValue() ? (
              <Box
                component={"img"}
                src={value.split(",")?.[0] as string}
                alt="productImg"
                height={42}
                sx={{ objectFit: "contain" }}
              />
            ) : (
              <Box
                component={"img"}
                src="/images/image_not_found.png"
                alt="image-not-found"
                height={42}
                sx={{ objectFit: "contain" }}
              />
            )
          )
        }
      },
      {
        accessorKey: "name",
        header: t("product.label_name"),
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
          >
            <Typography>{data.getValue()}</Typography>
          </Box>
        ),
      },
      {
        accessorKey: "type",
        header: t("product.label_type"),
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
          >
            <Typography>{
              data.getValue() === 1 ? t("product.label_retail") : data.getValue() === 2 ? t("product.label_auction") : ""
            }</Typography>
          </Box>
        ),
      },
      {
        accessorKey: "status",
        header: t("product.label_status"),
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
          >
            <Typography>{
              data.getValue() === 1 ? t("product.label_draft") :
                data.getValue() === 2 ? t("product.label_listed") :
                  data.getValue() === 3 ? t('product.label_unlisted') : ""
            }</Typography>
          </Box>
        )
      },
      {
        accessorKey: "excluded",
        header: t("product.label_room_excluded")
      },
      {
        accessorKey: "updatedAt",
        header: t("product.label_updated_at"),
        cell: (data) => {
          const value = data.getValue() as string;
          return (
            format(new Date(Number(value) * 1000).toISOString() as string, "yyyy-MM-dd HH:mm:ss")
          )
        }
      },
      {
        accessorKey: "createdAt",
        header: t("product.label_created_at"),
        cell: (data) => {
          const value = data.getValue() as string;
          return (
            format(new Date(Number(value) * 1000).toISOString() as string, "yyyy-MM-dd HH:mm:ss")
          )
        }
      },
    ],
    /* eslint-disable react-hooks/exhaustive-deps */
    [router, t]
  );

  const defaultData = React.useMemo<IProduct[]>(() => [], []);

  const table = useReactTable({
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination, sorting },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  return <PaginationTable table={table} fullWidth isLoading={isLoading} msg={t("product.title_empty")} />;
};

export default ProductTable;
