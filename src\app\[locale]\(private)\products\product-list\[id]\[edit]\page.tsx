"use client"
import CancelButton from '@/components/buttons/CancelButton';
import SaveButton from '@/components/buttons/SaveButton';
import PageHeader from '@/components/PageHeader';
import { Box, Typography } from '@mui/material';
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import React, { useEffect, useState } from 'react';
import EditInformation from './EditInformation';
import EditSelling from './EditSelling';
import {ProductDetailDto} from '@/interface/IProductDetailDto'
import xior from "xior";
import {showErrorPopUp} from "@/utils/toast";

interface Props {
    params: { id: string };
}

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
  
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  }
  
  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }


const ProductDetail = ({params}:Props) =>{
    const t = useTranslations("product");
    const router = useRouter();
    const [value, setValue] = React.useState(0);
    const [productDetialDto,setProductDetialDto] = useState<ProductDetailDto | undefined>(undefined);
    const [updateProductDto, setUpdateProductDto] = useState<ProductDetailDto | undefined>(undefined);
    const {showToast} = showErrorPopUp()
    // const [updateProductDto,setPpdateProductDto] = useState<ProductDetailDto>({
    //   name:         productDetialDto!.name,
    //   owner:        productDetialDto!.owner,
    //   description:  productDetialDto!.description,
    //   thumbnail:    productDetialDto!.thumbnail,
    //   createdAt:    productDetialDto!.createdAt,
    //   compareAt:    productDetialDto!.compareAt,
    //   status:       productDetialDto!.status,
    //   productType:  productDetialDto!.productType,
    //   price:        productDetialDto!.price,
    //   category:     productDetialDto!.category,
    //   quantity:     productDetialDto!.quantity,
    //   type:         productDetialDto!.type,
    //   currency:     productDetialDto!.currency,
    //   isDigital:    productDetialDto!.isDigital,
    // });

    const handleGetProductById = async() => {
      const response = await xior.get(`/api/products/${params.id}`)
      setProductDetialDto(response.data)
    }

    const handleUpdateProductApi = async() =>{
      try{

        const response = await xior.put(`/api/products/${params.id}`,updateProductDto)
        // setUpdateProductDto(response.data)
        console.log('update product details', response.data) 
        showToast(`${updateProductDto?.name} updated successfully`);
        router.push(ROUTES.PRODUCT_LIST);
      }catch(error){

        console.log("update product data error",error)
        showToast("update product error, please try again.");

      }
    }

    const handleUpdateProductDtoChange = (updateProductDto:ProductDetailDto) =>{
      setUpdateProductDto(updateProductDto)
    }

    console.log('check data',productDetialDto)

    useEffect(() => {
      handleGetProductById()
    }, [params.id]);

      useEffect(() => {
        if (productDetialDto) {
            setUpdateProductDto({ ...productDetialDto });
        }
    }, [productDetialDto]);

    console.log("Check updateProductDto",updateProductDto)
      const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
      };

    return(
        <>
        <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
            <PageHeader title={`${t("label_title")} > ${t("label_name")} : ${productDetialDto?.name}`}>
                <>
                <CancelButton onAction={() => router.push(ROUTES.PRODUCT_LIST)} />
                <SaveButton  onAction={handleUpdateProductApi}/>
                </>
            </PageHeader>
            <Box flex={1} padding="26px 34px">
                {/* <Typography>{params.id}</Typography> */}
            <Box sx={{ width: '100%' }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                    <Tab label="INFORMATION" {...a11yProps(0)} />
                    <Tab label="SELLING" {...a11yProps(1)} />
                    </Tabs>
                </Box>
                <CustomTabPanel value={value} index={0}>
                    {
                        updateProductDto &&
                            <EditInformation 
                            updateProductDto={updateProductDto}
                            handleUpdateProductDtoChange={handleUpdateProductDtoChange}
                            />
                    }
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                  {
                    updateProductDto &&
                    <EditSelling 
                    updateProductDto={updateProductDto}
                    handleUpdateProductDtoChange={handleUpdateProductDtoChange}
                    />
                  }
                </CustomTabPanel>
                </Box>
            </Box>
        </Box>    
        </>
    )
}

export default ProductDetail