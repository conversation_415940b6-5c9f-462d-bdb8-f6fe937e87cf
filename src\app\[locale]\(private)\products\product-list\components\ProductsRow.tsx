"use client"
import * as React from 'react';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Checkbox, Typography } from '@mui/material';
import EditButton from '@/components/buttons/EditButton';
import DeleteButton from '@/components/buttons/DeleteButton'
import { IProduct } from "@/interface/IProduct";
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import Modal from '@mui/material/Modal';
import xior from "xior";
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { useState } from 'react';

type Props = {
  allProductsDto:IProduct
  getAllProductsApi:()=> void
}

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};


const ProductsRow = ({allProductsDto, getAllProductsApi}:Props) =>{

  const [open, setOpen] = React.useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const [statusOpen, setStatusOpen] = useState<boolean>(false);
  const handleStatusOpen = () => setStatusOpen(true);
  const handleStatusClose = () => setStatusOpen(false);

  const [listedstatusOpen, setListedStatusOpen] = useState<boolean>(false);
  const handleListedStatusOpen = () => setListedStatusOpen(true);
  const handleListedStatusClose = () => setListedStatusOpen(false);

  const router = useRouter();

  const handleEditClick = () =>{
    router.push(`/products/product-list/${allProductsDto.id}/edit`)
  }

  const handleDeleteProductApi = async () =>{
    
    try{
      const response = await xior.delete(`/api/products/${allProductsDto.id}`)
      console.log('delete product api', response.data);
      getAllProductsApi()
      setOpen(false)
    }catch(error){
      console.log('delete product api error', error);
    }
  }

  const handleProductStatusChangeApi = async () =>{

    const productData = {}

    try{
      const response = await xior.put(`/api/products/status/${allProductsDto.id}`,productData)
      console.log('update product status api', response.data)
      getAllProductsApi()
      setStatusOpen(false)
    }catch(error){
      console.log('change product status api error', error);
    }
  }

  const handleProductListedStatusChangeApi = async () =>{
    const productData = {}

    try{

      const response = await xior.put(`/api/products/listedstatus/${allProductsDto.id}`,productData)
      console.log('update product liste status api', response.data)
      getAllProductsApi()
      setListedStatusOpen(false)
    }catch(error){
      console.log('change product list status api error', error);
    }
  }

  const renderType = () =>{
    if(allProductsDto.type === 1){
      return(
        <>
          <Typography>Retails</Typography>
        </>
      )
    }else if(allProductsDto.type === 2){
      <>
        <Typography>Auction</Typography>
      </>
    }
  }

  const renderStatus = () =>{
    if(allProductsDto.status === 1){
      return(
        <>
          <Typography>Draft</Typography>
        </>
      )
    }else if(allProductsDto.status === 2){
      return(
        <>
          <Typography>Listed</Typography>
        </>
      )
    }else if(allProductsDto.status === 3){
      return(
        <>
        <Typography>Unlisted</Typography>
        </>
      )
    }
  }

  const renderProductStatusIcon = () =>{
      if(allProductsDto.status === 2 || allProductsDto.status === 3){
        return(
          <>
            <Button>
              <VisibilityIcon 
              onClick={handleStatusOpen}
              />
            </Button>
          </>
        )
      }else if(allProductsDto.status === 1){
        return(
          <>
             <Button>
              <VisibilityOffIcon
              onClick={handleListedStatusOpen}
              />
            </Button>
          </>
        )
      }else{
        return(
          <></>
        )
      }
  }

  // const renderProductExclustive = () =>{
  //   if(allProductsDto.memberGroupName === null){
  //     return(
  //       <>
  //       <Typography>N/A</Typography>
  //       </>
  //     )
  //   }else{
  //     return(
  //       <>
  //       <Typography>{allProductsDto.memberGroupName}</Typography>
  //       </>
  //     )
  //   }
  // }

  const renderProductExclustive = () => {
    if (!allProductsDto.memberGroupNames || allProductsDto.memberGroupNames.length === 0) {
        return (
            <>
                <Typography>N/A</Typography>
            </>
        );
    } else {
        return (
            <>
                {allProductsDto.memberGroupNames.map((groupName, index) => (
                    <Typography key={index}>{groupName}</Typography>
                ))}
            </>
        );
    }
};

  const createdAt = allProductsDto.createdAt;
  const updatedAt = allProductsDto.updatedAt;

    const createdAtformattedDate = createdAt ? 
    format(new Date(createdAt * 1000), "yyyy-MM-dd HH:mm:ss") : 
    "N/A"; // 或者你可以用其他你想要的默認值

  const updatedAtformattedDate = updatedAt ? 
    format(new Date(updatedAt * 1000), "yyyy-MM-dd HH:mm:ss") : 
    "N/A"; // 同樣的處理

    return(
        <>
            <TableRow>
              <TableCell>
              <Checkbox
              />
              </TableCell>
              <TableCell>
                <Box sx={{
                  display:"flex",
                  justifyContent:"center",
                }}>
                <EditButton
                iconOnly={true}
                onClick={handleEditClick}
                />&nbsp;&nbsp;
                <DeleteButton
                onClick={handleOpen}
                />
                {
                  renderProductStatusIcon()
                }
                </Box>
              </TableCell>
              <TableCell>
              <Box>
                <img src={allProductsDto.thumbnail}
                width="100px"
                />
                </Box>
              </TableCell>
              <TableCell>
              <Typography>
                {allProductsDto.name}
                </Typography>
                </TableCell>
              <TableCell>{renderType()}</TableCell>
              <TableCell>{renderStatus()}</TableCell>
              <TableCell>{renderProductExclustive()}</TableCell>
              <TableCell>{updatedAtformattedDate}</TableCell>
              <TableCell>{createdAtformattedDate}</TableCell>
            </TableRow>
            {/* <Button onClick={handleOpen}>Open modal</Button> */}
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box  sx={{
            ...style,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center', 
            textAlign: 'center', 
          }}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
              Are you sure to delete?
          </Typography>
          <Typography id="modal-modal-description" sx={{ mt: 2 }}>
            <Box sx={{
              display: "flex",
              justifyContent: "space-between",
              gap: 2, 
              width: '100%',
            }}>
            <Button onClick={handleClose}>Stay in page</Button>
            <Button variant="contained" onClick={handleDeleteProductApi}>Delete</Button>
            </Box>
          </Typography>
        </Box>
      </Modal>
      <Modal
        open={statusOpen}
        onClose={handleStatusClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box  sx={{
            ...style,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center', 
            textAlign: 'center', 
          }}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
              Are you sure to change product status to draft?
          </Typography>
          <Typography id="modal-modal-description" sx={{ mt: 2 }}>
            <Box sx={{
              display: "flex",
              justifyContent: "space-between",
              gap: 2, 
              width: '100%',
            }}>
            <Button onClick={handleStatusClose}>Stay in page</Button>
            <Button variant="contained" onClick={handleProductStatusChangeApi}>Yes</Button>
            </Box>
          </Typography>
        </Box>
      </Modal>
      <Modal
        open={listedstatusOpen}
        onClose={handleListedStatusClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box  sx={{
            ...style,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center', 
            textAlign: 'center', 
          }}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
              Are you sure to change product status to Listed?
          </Typography>
          <Typography id="modal-modal-description" sx={{ mt: 2 }}>
            <Box sx={{
              display: "flex",
              justifyContent: "space-between",
              gap: 2, 
              width: '100%',
            }}>
            <Button onClick={handleListedStatusClose}>Stay in page</Button>
            <Button variant="contained" onClick={handleProductListedStatusChangeApi}>Yes</Button>
            </Box>
          </Typography>
        </Box>
      </Modal>
        </>
    )
}

export default ProductsRow