"use client";
import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  PaginationState,
  SortingState,
  ColumnDef,
} from "@tanstack/react-table";
import { Box, Button, Typography } from "@mui/material";
import { useTranslations } from "next-intl";
import PaginationTable from "@/components/PaginationTable";
import { IRoom } from "@/interface/IRoom";
import { useRouter } from "next/navigation";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import xior from "xior";
import { IListResponse } from "@/interface/IListResponse";
import { format } from "date-fns";
import { ROUTES, UNITY_ENDPOINT } from "@/utils/constants";

import EditButton from "@/components/buttons/EditButton";

const RoomTable = () => {
  const t = useTranslations("");
  const router = useRouter();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const {
    data,
    isLoading
  } = useQuery({
    queryKey: ["rooms", { pagination, sorting }],
    queryFn: async () =>
      xior
        .get<IListResponse<IRoom>>("/api/rooms", {
          params: {
            page: pagination.pageIndex + 1,
            size: pagination.pageSize,
          },
        })
        .then((res) => res.data),
    placeholderData: keepPreviousData,
    retry: 3,
  });

  const columns = useMemo<ColumnDef<IRoom>[]>(
    () => [
      {
        accessorKey: "roomId",
        header: "",
        cell: (data) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"center"}
            mx={1}
            key={`tag-${data.row.original.id}-actions`}
          >
            <EditButton
              iconOnly={true}
              onClick={() => router.push(`/room-information/${data.row.original.id}/edit`)}
            />
          </Box>
        ),
      },
      {
        accessorKey: "thumbnailUrl",
        header: t("room_information.label_thumbnail"),
        cell: (data) =>
          data.getValue() ? (
            <Box
              component={"img"}
              src={data.getValue() as string}
              alt="room-thumbnail"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          ) : (
            <Box 
              component={"img"}
              src="/images/image_not_found.png"
              alt="image-not-found"
              height={42}
              sx={{ objectFit: "contain" }}
            />
          )
      },
      {
        accessorKey: "roomName",
        header: t("room_information.label_room_name"),
        cell: (data: any) => (
          <Box
            display={"flex"}
            flexDirection={"row"}
            justifyContent={"space-between"}
          >
            <Typography>{data.getValue()}</Typography>
          </Box>
        ),
      },
      {
        accessorKey: "roomUrl",
        header: t("room_information.label_room_url"),
        cell: (data) => UNITY_ENDPOINT + data.getValue(),
      },
      {
        accessorKey: "visibleToPublic",
        header: t("room_information.label_room_status"),
        cell: (data) => (data.getValue() ? "Visible" : "Hidden"),
      },
      {
        accessorKey: "memberGroups",
        header: t("room_information.label_room_exclusive_to"),
        cell: (data) =>
          (data.getValue() as { name: string }[])
            .map((item) => item.name)
            .join(", "),
      },
      {
        accessorKey: "updatedAt",
        header: t("room_information.label_updated_at"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
      {
        accessorKey: "createdAt",
        header: t("room_information.label_created_at"),
        cell: (data) => {
          const datetime = data.getValue()
          if (!datetime) return "-"
          if (datetime) {
            return format(data.getValue() as string, "yyyy-MM-dd HH:mm:ss")
          }
        }
      },
    ],
    [router, t]
  );

  const defaultData = React.useMemo<IRoom[]>(() => [], []);

  const table = useReactTable({
    data: data?.items ?? defaultData,
    columns,
    rowCount: data?.count,
    state: { pagination, sorting },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  return <PaginationTable table={table} fullWidth isLoading={isLoading} msg={t("room_information.title_empty")} />;
};

export default RoomTable;
