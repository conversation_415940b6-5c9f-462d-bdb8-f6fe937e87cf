"use client";
import { Box, TableCell, TableRow } from "@mui/material";
import React from "react";
import { OrderProduct } from "@/interface/ISalesOrderDtoDetails";
import Image from "next/image";


type Props = {
  orderProduct: OrderProduct
}

const SalesOrderDetailRow = ({ orderProduct }: Props) => {

  return (
    <>
      <TableRow>
        <TableCell>
          <Box>
            {/* <img src={orderProduct.thumbnail}
          width="100px"
        /> */}
            <Image
              src={orderProduct.thumbnail}
              alt="Product Image"
              width={100}
              height={80}
            />
          </Box>
        </TableCell>
        <TableCell>{orderProduct.name}</TableCell>
        <TableCell>{orderProduct.quantity}</TableCell>
        <TableCell>${orderProduct.price}</TableCell>
        <TableCell>${(Number(orderProduct.quantity) || 0) * (Number(orderProduct.price) || 0)}</TableCell>
      </TableRow>
    </>
  )
}

export default SalesOrderDetailRow;