"use client";
import Table from "@mui/material/Table";
import { TableBody, TableCell, TableHead, TableRow, Typography } from "@mui/material";
import SalesOrderDetailRow from "@/app/[locale]/(private)/sales-orders/[OrderId]/components/SalesOrderDetailRow";
import { SalesOrderDetailsDto } from "@/interface/ISalesOrderDtoDetails"
import React, { useEffect, useState } from "react";
import xior from "xior";

interface Props {
  params: { OrderId: string };
}

const SalesOrderDetailTable = ({ params }: Props) => {

  const [getSalesOrderDetailsDtoList, setGetSalesOrderDetailsDtoList] = useState<SalesOrderDetailsDto | undefined>(undefined);

  const fetchSalesOrdersDetails = async () => {
    const response = await xior.get(`/api/sales-orders/${params.OrderId}`)
    setGetSalesOrderDetailsDtoList(response.data);
  }

  useEffect(() => {
    fetchSalesOrdersDetails()
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const productType = getSalesOrderDetailsDtoList?.orderProduct[0]?.productType;

  const renderProductType = () => {
    if (productType === 1) {
      return (
        <>
          <TableCell colSpan={5} sx={{
            backgroundColor: "#E7E7E7",
            padding: "5px"
          }}>
            <Typography sx={{ fontWeight: 'bold' }}>Physical</Typography>
          </TableCell>
        </>
      )
    } else if (productType === 2) {
      return (
        <>
          <TableCell colSpan={5} sx={{
            backgroundColor: "#E7E7E7",
            padding: "5px"
          }}>
            <Typography sx={{ fontWeight: 'bold' }}>Digital</Typography>
          </TableCell>
        </>
      )
    } else {
      return null;
    }
  }

  return (
    <>
      <Table sx={{
        minWidth: 650
      }} aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell>Thumbnail</TableCell>
            <TableCell>Product Name</TableCell>
            <TableCell>Qty</TableCell>
            <TableCell>Unit Price(USD)</TableCell>
            <TableCell>Total(USD)</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow sx={{
            height: "10px"
          }}>
            {
              renderProductType()
            }
          </TableRow>
          {/* {
              iSalesOrderDetailDto &&
              iSalesOrderDetailDto.transactionItems.map((value)=>(
                <SalesOrderDetailRow key={value.tpid} transactionItems={value}/>
              ))
            } */}
          {
            getSalesOrderDetailsDtoList &&
            getSalesOrderDetailsDtoList.orderProduct.map((value) => (
              <SalesOrderDetailRow key={value.id} orderProduct={value} />
            ))
          }
        </TableBody>
      </Table>
    </>
  )
}

export default SalesOrderDetailTable;