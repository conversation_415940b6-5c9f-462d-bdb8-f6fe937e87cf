"use client";
import {Typography} from "@mui/material";
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import SalesOrderDetailTable from "@/app/[locale]/(private)/sales-orders/[OrderId]/components/SalesOrderDetailTable";
import {AllSalesOrder} from "@/interface/ISalesOrderDto"

type Props = {
  allSalesOrder: AllSalesOrder,
  params: { OrderId: string };
}

const SalesOrderDetailsContainer = ({allSalesOrder,params}:Props) =>{

  // const Item = styled(Paper)(({ theme }) => ({
  //   backgroundColor: '#fff',
  //   ...theme.typography.body2,
  //   padding: theme.spacing(1),
  //   textAlign: 'center',
  //   color: theme.palette.text.secondary,
  //   ...theme.applyStyles('dark', {
  //     backgroundColor: '#1A2027',
  //   }),
  // }));

  const Item = styled('div')(({ theme }) => ({
    backgroundColor: '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    boxShadow: 'none', // 去掉阴影
    border: '1px solid transparent', // 可选：去掉边框
    ...theme.applyStyles('dark', {
      backgroundColor: '#1A2027',
    }),
  }));

  const itemStyles = {
    color:"black",
    display:"flex",
  }

  const renderDisplayDeliveryAddress = () =>{
    if(allSalesOrder.deliveryNoteId){
      return(
        <>
          <Grid container spacing={2} sx={{
            mt:"2px"
          }}>
            <Grid item xs={4}>
              <Item sx={itemStyles} >
                <Typography>
                  Recipient Name:
                </Typography>&nbsp;&nbsp;
                <Typography>{allSalesOrder.clientName}</Typography></Item>
            </Grid>
            <Grid item xs={4}>
              <Item sx={itemStyles}>
                <Typography>
                  Recipient Contact No:
                </Typography>&nbsp;&nbsp;
                <Typography>{allSalesOrder.clientContactN}</Typography></Item>
            </Grid>
            <Grid item xs={12}>
              <Item sx={itemStyles}>
                {/* {
                  allSalesOrder &&
                    <SalesOrderDeliveryAddress key={allSalesOrder.orderId} allSalesOrder={allSalesOrder}/>
                } */}
                <Typography>Delivery Address:</Typography>&nbsp;
                <Typography>{allSalesOrder.deliveryAddressAddressline1},</Typography>&nbsp;
                <Typography>{allSalesOrder.deliveryAddressAddressline2},</Typography>&nbsp;
                <Typography>{allSalesOrder.deliveryAddressCountry}</Typography>
              </Item>
            </Grid>
            <Grid item xs={4}>
              <Item sx={itemStyles}>
                <Typography>
                  Delivery Note:
                </Typography>&nbsp;&nbsp;
                <Typography>{allSalesOrder.deliveryNoteId}</Typography></Item>
            </Grid>
          </Grid>
        </>
      );
    }else {
      return (
        <>
        </>
      );
    }
  }

  return (
    <>
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Item sx={itemStyles}>
              <Typography>
              Client Name:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.clientName}</Typography></Item>
          </Grid>
          <Grid item xs={4}>
            <Item sx={itemStyles}>
              <Typography>
                Client Email:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.clientEmail}</Typography></Item>
          </Grid>
          <Grid item xs={4}>
            <Item sx={itemStyles}>
              <Typography>
                Client Contact No.:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.clientContactN}</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{
          mt:"2px"
        }}>
          <Grid item xs={12}>
            <Item sx={itemStyles}>
                  <Typography>Billing Address:</Typography>&nbsp;&nbsp;
                <Typography>{allSalesOrder.billingAddressAddressline1},</Typography>&nbsp;&nbsp;
                  <Typography>{allSalesOrder.billingAddressAddressline2},</Typography>&nbsp;&nbsp;
                <Typography>{allSalesOrder.billingAddressCountry}</Typography>
            </Item>
          </Grid>
        </Grid>
        {
          renderDisplayDeliveryAddress()
        }
        <Box sx={{
          mt:"15px",
          mb:"15px"
        }}>

        {/* {
          allSalesOrder &&
            <SalesOrderDetailTable key={allSalesOrder.orderNo}  />
        } */}
        <SalesOrderDetailTable params={params}/>
        </Box>
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                Gross Total:
              </Typography>&nbsp;&nbsp;
              <Typography>${allSalesOrder.grossTotal.toLocaleString()}</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{mt:"2px"}}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                Promo Code:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.promoCode}</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{mt:"2px"}}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                Promo Code Discount:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.promoCodeDiscount} (USD$)</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{mt:"2px"}}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                TAX/VAT:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.taxVat}</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{mt:"2px"}}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                Grand Total:
              </Typography>&nbsp;&nbsp;
              <Typography>${allSalesOrder.grandTotal.toLocaleString()}</Typography></Item>
          </Grid>
        </Grid>
        <Grid container spacing={2} sx={{mt:"2px"}}>
          <Grid item xs={4}>
            <Item  sx={itemStyles} >
              <Typography>
                Payment Method:
              </Typography>&nbsp;&nbsp;
              <Typography>{allSalesOrder.paymentMethod}</Typography></Item>
          </Grid>
        </Grid>
      </Box>
    </>
  );
}

export default SalesOrderDetailsContainer;