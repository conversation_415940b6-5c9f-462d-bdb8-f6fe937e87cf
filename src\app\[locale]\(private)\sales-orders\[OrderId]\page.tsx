"use client";
import type { NextPage } from "next";
import {useTranslations} from "next-intl";
import {Box} from "@mui/material";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import SalesOrderDetailsContainer
  from "@/app/[locale]/(private)/sales-orders/[OrderId]/components/SalesOrderDetailsContainer";
import mockData from "../response.json";
import {useEffect, useState} from "react";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import { SalesOrderDto } from "@/interface/ISalesOrderDto";
import xior from "xior";

interface Props {
    params: { OrderId: string };
}

const SalesOrderDetail: NextPage<Props> = ({params}:Props) => {
  const t = useTranslations("sales_orders");
  const [salesOrderDetailDtolist,setSalesOrderDetailDto] = useState<ISalesOrderDto[] | undefined>(undefined);
  const [getSalesOrderList,setGetSalesOrderList] = useState<SalesOrderDto| undefined>(undefined);


  const fetchSalesOrders = async () =>{
    const response = await xior.get('/api/sales-orders');
    setGetSalesOrderList(response.data);

}

  useEffect(() => {
    fetchSalesOrders()
  }, []);

    return (
      <>
        <Box sx={{height: "100%"}}>
          <PageHeader title={`${t("orders_list")}  >  ${params.OrderId}`}>
            <>
              <ExportButton/>
            </>
          </PageHeader>
          <Box flex={1} padding="26px 34px">
            {/* {
              salesOrderDetailDtolist &&
              salesOrderDetailDtolist.filter((value)=>(
                value.orderId.includes(params.OrderId)
              ))
              .map((value)=>(
                <SalesOrderDetailsContainer key={value.orderId} iSalesOrderDetailDto={value}/>
              ))
            } */}
            {
              getSalesOrderList?.allSalesOrders &&
              getSalesOrderList?.allSalesOrders.filter((value)=>(
                  value.orderNo.includes(params.OrderId)
              ))
              
              .map((value)=>(
                <SalesOrderDetailsContainer key={value.orderNo} allSalesOrder={value} params={params}/>
              ))
            }
          </Box>
        </Box>
      </>

    );
}

export default SalesOrderDetail;