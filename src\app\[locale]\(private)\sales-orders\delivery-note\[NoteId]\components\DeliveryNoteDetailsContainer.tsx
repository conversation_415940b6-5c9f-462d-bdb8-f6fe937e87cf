import {ISalesOrderDto} from "@/interface/ISalesOrder";
import Grid from "@mui/material/Grid";
import {Typography} from "@mui/material";
import {styled} from "@mui/material/styles";
import DeliveryNoteDetailsTable
  from "@/app/[locale]/(private)/sales-orders/delivery-note/[NoteId]/components/DeliveryNoteDetailsTable";
import {NoteIDResult} from "@/interface/ISalesOrderNoteId";

type Props = {
  noteIdresult :NoteIDResult
  params: { NoteId: string };
}

const DeliveryNoteDetailsContainer = ({noteIdresult,params}: Props) => {
  console.log("noteId",params.NoteId)
  const Item = styled('div')(({theme}) => ({
    backgroundColor: '#fff',
    ...theme.typography.body2,
    padding: theme.spacing(1),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    boxShadow: 'none', // 去掉阴影
    border: '1px solid transparent', // 可选：去掉边框
    ...theme.applyStyles('dark', {
      backgroundColor: '#1A2027',
    }),
  }));

  const itemStyles = {
    color: "black",
    display: "flex",
  }

  return (
    <>
      <Grid container spacing={2} sx={{
        mt: "2px",
        mb: "2px"
      }}>
        <Grid item xs={4}>
          <Item sx={itemStyles}>
            <Typography>
              Recipient Name:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.clientName}</Typography></Item>
        </Grid>
        <Grid item xs={4}>
          <Item sx={itemStyles}>
            <Typography>
              Recipient Contact No:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.clientContactN}</Typography></Item>
        </Grid>
      </Grid>
      <Grid container spacing={2} sx={{
        mt: "2px",
        mb: "15px"
      }}>
        <Grid item xs={12}>
          <Item sx={itemStyles}>
            <Typography>
              Delivery Address:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.deliveryAddressAddressline1},</Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.deliveryAddressAddressline2},</Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.deliveryAddressCountry}</Typography>
          </Item>
        </Grid>
      </Grid>
      {/* {
        iSalesOrderDetailDto &&
          <DeliveryNoteDetailsTable key={iSalesOrderDetailDto.deliveryNoteId}
                                    iSalesOrderDetailDto={iSalesOrderDetailDto}/>
      } */}
      {
         <DeliveryNoteDetailsTable params={params}/>
      }
        <Grid item xs={4} sx={{
         mt:"10px"
        }}>
          <Item sx={itemStyles}>
            <Typography>
              Delivery Status:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.deliveryStatus}</Typography></Item>
        </Grid>
        <Grid item xs={4}>
          <Item sx={itemStyles}>
            <Typography>
             Remarks:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.remarks}</Typography></Item>
        </Grid>
        <Grid item xs={4}>
          <Item sx={itemStyles}>
            <Typography>
              Handled Staff:
            </Typography>&nbsp;&nbsp;
            <Typography>{noteIdresult.handlingStaff}</Typography></Item>
        </Grid>
    </>
  )
}

export default DeliveryNoteDetailsContainer