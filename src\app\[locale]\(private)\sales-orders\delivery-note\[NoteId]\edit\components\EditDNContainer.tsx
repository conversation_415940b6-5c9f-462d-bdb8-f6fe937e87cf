"use client";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import {styled} from "@mui/material/styles";
import {Box, Button, FormControl, InputLabel, MenuItem, Select, SelectChangeEvent, TextareaAutosize, TextField, Typography} from "@mui/material";
import EditDNTable from "@/app/[locale]/(private)/sales-orders/delivery-note/[NoteId]/edit/components/EditDNTable";
import {useState, ChangeEvent } from "react";
import {NoteIDResult} from "@/interface/ISalesOrderNoteId";
import SaveButton from "@/components/buttons/SaveButton";
import AddUpdateProductSuccessSnackBar from "@/components/AddUpdateProductSuccessSnackBar"
import {showErrorPopUp} from "@/utils/toast";
import { ROUTES } from "@/utils/constants";
import { useRouter } from "next/navigation";

type Props = {
  noteIDResult: NoteIDResult
  params: { NoteId: string };
}

const EditDNContainer = ({noteIDResult,params}:Props) =>{

  const [updateSaleOrderDetail,setUpdateSaleOrderDetail] = useState<NoteIDResult>({
    clientName:                  noteIDResult.clientName,
    clientEmail:                 noteIDResult.clientEmail,
    clientContactN:              noteIDResult.clientContactN,
    deliveryAddressAddressline1: noteIDResult.deliveryAddressAddressline1,
    deliveryAddressAddressline2: noteIDResult.deliveryAddressAddressline2,
    deliveryAddressCountry:      noteIDResult.deliveryAddressCountry,
    deliveryStatus:              noteIDResult.deliveryStatus,
    handlingStaff:               noteIDResult.handlingStaff,
    remarks:                     noteIDResult.remarks,
    updatedAt:                   noteIDResult.updatedAt
  });

  const [snackbarOpen,setSnackbarOpen] = useState<boolean>(false);
  const {showToast} = showErrorPopUp() 
  const router = useRouter();
  const handleUpdateNoteDetailApi = async (event: React.FormEvent<HTMLFormElement>) => {
    try{

      event.preventDefault();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/sales-orders/updateDeliveryNoteDetails/${params.NoteId}`, {
        method: 'PUT',
        body: JSON.stringify(updateSaleOrderDetail),
        headers: {
          'Content-Type': 'application/json',
      }, 
    });
       setSnackbarOpen(true);
      const data = await response.json();
      console.log(data);
      showToast("Successfully Updated");
      router.push(`${ROUTES.DELIVERY_NOTE}/${params.NoteId}`);
    }catch(error){
      console.log('upate note details error',error)
      showToast("upate note details error, please try again.");
    }

  }

  const handleSnackbarClose = () =>{
    setSnackbarOpen(false)
  }


  const handleUpdateSaleOrderDetail = (updateSaleOrderDetail:NoteIDResult) =>{
    setUpdateSaleOrderDetail(updateSaleOrderDetail)
  }

const handleUpdateProductDtoForm = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement >) => {
    const { name, value } = event.target;

    handleUpdateSaleOrderDetail({
        ...updateSaleOrderDetail,
        [name]: value,
    });
};

const handleSelectChange = (event: SelectChangeEvent<string>) => {
  const { name, value } = event.target;

  handleUpdateSaleOrderDetail({
      ...updateSaleOrderDetail,
      [name]: value,
  });
};

  return(
    <>
      <Box
      component="form"
      sx={{ '& > :not(style)': { m: 1, width: '25ch' } }}
      noValidate
      autoComplete="off"
      onSubmit={handleUpdateNoteDetailApi}
    >
      <Typography>Recipient Name:</Typography>
      <TextField 
      name="clientName"
      value={updateSaleOrderDetail.clientName}
      onChange={handleUpdateProductDtoForm}
      />
      <Typography>Recipient Contact No:
      </Typography>
      <TextField 
      name="clientContactN"
      value={updateSaleOrderDetail.clientContactN}
      onChange={handleUpdateProductDtoForm}
      />
      <Typography>Delivery Address:</Typography>
      <TextField 
      name="deliveryAddressAddressline1"
      label="Address Line 1"
      value={updateSaleOrderDetail.deliveryAddressAddressline1}
      onChange={handleUpdateProductDtoForm}
      />
      <TextField 
      name="deliveryAddressAddressline2"
      label="Address Line 2"
      value={updateSaleOrderDetail.deliveryAddressAddressline2}
      onChange={handleUpdateProductDtoForm}
      />
      <TextField 
      name="deliveryAddressCountry"
      label="Country / Region"
      value={updateSaleOrderDetail.deliveryAddressCountry}
      onChange={handleUpdateProductDtoForm}
      />
      {/* {
        iSalesOrderDetailDto &&
          <EditDNTable key={iSalesOrderDetailDto.deliveryNoteId}
                                    iSalesOrderDetailDto={iSalesOrderDetailDto}/>
      } */}
      <EditDNTable params={params}/>
      <Typography>Delivery Status:</Typography>
       <FormControl fullWidth>
        <Select
          labelId="demo-simple-select-label"
          id="demo-simple-select"
          name="deliveryStatus"
          value={updateSaleOrderDetail.deliveryStatus}
          onChange={handleSelectChange}
        >
          <MenuItem value="delivered">Delivered</MenuItem>
          <MenuItem value="pending">Pending</MenuItem>
          <MenuItem value="cancelled">Cancelled</MenuItem>
        </Select>
      </FormControl>

    <Typography>Remark:</Typography>
      <TextareaAutosize
      name="remarks"
      value={updateSaleOrderDetail.remarks || ""}
      onChange={handleUpdateProductDtoForm}
      />
      <Typography>Handled Satff*:</Typography>
      <TextField 
      name="handlingStaff"
      value={updateSaleOrderDetail.handlingStaff || ""}
      onChange={handleUpdateProductDtoForm}
      />
      <Button type="submit" variant="contained" sx={{mt:2}}>
          Save
        </Button>
      </Box>
      {/* <AddUpdateProductSuccessSnackBar open={snackbarOpen} handleClose={handleSnackbarClose}/> */}
    </>
  )
}

export default EditDNContainer