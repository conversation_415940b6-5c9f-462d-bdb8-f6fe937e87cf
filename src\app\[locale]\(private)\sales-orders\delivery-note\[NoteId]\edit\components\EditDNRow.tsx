import { DNProduct } from "@/interface/ISaleOrderDNProduct";
import { Box, TableCell, TableRow } from "@mui/material";
import Image from "next/image";
import React from "react";

type Props = {
  dNProduct: DNProduct
}

const EditDNRow = ({ dNProduct }: Props) => {
  return (
    <>
      <TableRow>
        <TableCell>
          <Box>
            {/* <img src={dNProduct.thumbnail}
                 width="100px"
            /> */}
            <Image
              src={dNProduct.thumbnail}
              alt="Product Image"
              width={100}
              height={80}
            />ƒ
          </Box>
        </TableCell>
        <TableCell>{dNProduct.name}</TableCell>
        <TableCell>{dNProduct.quantity}</TableCell>
      </TableRow>
    </>
  )
}

export default EditDNRow