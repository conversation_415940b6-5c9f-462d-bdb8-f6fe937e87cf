import Table from "@mui/material/Table";
import { TableBody, TableCell, TableHead, TableRow } from "@mui/material";
import React, { useEffect, useState } from "react";
import EditDNRow from "@/app/[locale]/(private)/sales-orders/delivery-note/[NoteId]/edit/components/EditDNRow";
import { SalesOrderDNProduct } from "@/interface/ISaleOrderDNProduct";
import xior from "xior";

type Props = {
  params: { NoteId: string };
}

const EditDNTable = ({ params }: Props) => {

  const [getSalesOrderDNProduct, setSalesOrderDNProduct] = useState<SalesOrderDNProduct | undefined>(undefined);

  // const fetchSalesOrdersDetails = async () =>{
  //   const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/sales-orders/product/${params.NoteId}`, {
  //     method: 'GET',
  //     headers: {
  //       'Content-Type': 'application/json',
  //   }, 
  // });
  //   const data = await response.json();
  //   setSalesOrderDNProduct(data);
  // }

  const fetchSalesOrdersDetails = async () => {
    const response = await xior.get(`/api/sales-orders/product/${params.NoteId}`)
    setSalesOrderDNProduct(response.data);
  }


  useEffect(() => {
    fetchSalesOrdersDetails();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  return (
    <>
      <Table sx={{
        minWidth: 650
      }} aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell>Thumbnail</TableCell>
            <TableCell>Product Name</TableCell>
            <TableCell>Qty</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {/* {
            iSalesOrderDetailDto &&
            iSalesOrderDetailDto.transactionItems.map((value)=>(
              <EditDNRow key={value.tpid} transactionItems={value}/>
            ))
          } */}
          {
            getSalesOrderDNProduct &&
            getSalesOrderDNProduct.DNProduct.map((value) => (
              <EditDNRow key={value.id} dNProduct={value} />
            ))
          }
        </TableBody>
      </Table>
    </>
  )
}

export default EditDNTable