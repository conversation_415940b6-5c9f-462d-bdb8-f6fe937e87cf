"use client";
import { useTranslations } from "next-intl";
import { Box, Typography } from "@mui/material";
import PageHeader from "@/components/PageHeader";
import CancelButton from "@/components/buttons/CancelButton";
import SaveButton from "@/components/buttons/SaveButton";
import { ROUTES } from "@/utils/constants";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ISalesOrderDto } from "@/interface/ISalesOrder";
import mockData from "./response.json";
import EditDNContainer
  from "@/app/[locale]/(private)/sales-orders/delivery-note/[NoteId]/edit/components/EditDNContainer";
import { SalesOrderNoteId } from "@/interface/ISalesOrderNoteId";
import xior from "xior";

interface Props {
  params: { NoteId: string };
}

const EditDeliveryNotedDetail = ({ params }: Props) => {
  const t = useTranslations("sales_orders");
  const router = useRouter();
  // const [salesOrderDetailDtoList,setSalesOrderDetailDto] = useState<ISalesOrderDto[] | undefined>(undefined);
  const [getSalesOrderNoteId,setGetSalesOrderNoteId] = useState<SalesOrderNoteId | undefined>(undefined);
  // const fetchSalesOrdersDetails = async () =>{
  //   const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/sales-orders/delivery-note/${params.NoteId}`, {
  //     method: 'GET',
  //     headers: {
  //       'Content-Type': 'application/json',
  //   }, 
  // });
  //   const data = await response.json();
  //   setGetSalesOrderNoteId(data);
  // }

  const fetchSalesOrdersDetails = async () => {
    const response = await xior.get(`/api/sales-orders/delivery-note/${params.NoteId}`)
    setGetSalesOrderNoteId(response.data);
  }

  useEffect(() => {
    fetchSalesOrdersDetails();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);
  

  return (
    <>
      <Box sx={{ height: "100%" }}>
        <PageHeader title={`${t("delivery_note")}  >  ${params.NoteId}`}>
          <>
            <CancelButton
              onAction={() => router.push(ROUTES.DELIVERY_NOTE)}
            />
            {/* <SaveButton/> */}
          </>
        </PageHeader>
        <Box flex={1} padding="26px 34px">
          {/* {
            salesOrderDetailDtoList &&
            salesOrderDetailDtoList.filter((value)=>(
              value.deliveryNoteId.includes(params.NoteId)
            ))

              .map((value)=>(
              <EditDNContainer key={value.deliveryNoteId} iSalesOrderDetailDto={value}/>
            ))
          } */}
          {
            getSalesOrderNoteId?.noteIdResult &&
            getSalesOrderNoteId.noteIdResult.map((value) => (
              <EditDNContainer key={value.id} noteIDResult={value} params={params} />
            ))
          }
        </Box>
      </Box>
    </>
  )
}

export default EditDeliveryNotedDetail