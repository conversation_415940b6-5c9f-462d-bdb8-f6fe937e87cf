"use client";
import { Box, Typography } from "@mui/material";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import { useTranslations } from "next-intl";
import EditButton from "../../../../../../components/buttons/EditButton"
import { useRouter } from "next/navigation";
import { ROUTES } from "@/utils/constants";
import { useEffect, useState } from "react";
import { ISalesOrderDto } from "@/interface/ISalesOrder";
import mockData from "../../response.json";
import DeliveryNoteDetailsContainer
  from "@/app/[locale]/(private)/sales-orders/delivery-note/[NoteId]/components/DeliveryNoteDetailsContainer";
import { SalesOrderNoteId } from "@/interface/ISalesOrderNoteId";
import xior from "xior";

interface Props {
  params: { NoteId: string };
}

const DeliveryNotedDetail = ({ params }: Props) => {
  const t = useTranslations("sales_orders");
  const [getSalesOrderNoteId,setGetSalesOrderNoteId] = useState<SalesOrderNoteId | undefined>(undefined);
  

  const router = useRouter();
  const noteId = params.NoteId

  const handleEditClick = () => {
    const editUrl = ROUTES.DELIVERY_NOTE_EDIT(noteId);
    router.push(editUrl);
  }

  console.log('decode value',params.NoteId)
  const fetchSalesOrdersDetails = async () =>{
    
    const response = await xior.get(`/api/sales-orders/delivery-note/${params.NoteId}`)
    setGetSalesOrderNoteId(response.data);
  }

  useEffect(() => {
    fetchSalesOrdersDetails();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  return (
    <>
      <Box sx={{ height: "100%" }}>
        <PageHeader title={`${t("delivery_note")}  >  ${params.NoteId}`}>
          <>
            <ExportButton />
            <EditButton
              onClick={handleEditClick}
            />
          </>
        </PageHeader>
        <Box flex={1} padding="26px 34px">
        
          {
            getSalesOrderNoteId?.noteIdResult &&
            getSalesOrderNoteId?.noteIdResult.map((value) => (
              <DeliveryNoteDetailsContainer key={value.id} noteIdresult={value} params={params} />
            ))
          }
        </Box>
      </Box>
    </>
  )
}

export default DeliveryNotedDetail