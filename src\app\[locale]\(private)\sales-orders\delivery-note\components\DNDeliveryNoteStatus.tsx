import { FormControl, MenuItem, Select, SelectChangeEvent } from "@mui/material"

type Props = {
    deliveryStatusFilter:string,
    handleDeliveryStatusFilterChange:(deliveryStatusFilter:string)=> void
}

const DNDeliveryNoteStatus = ( {deliveryStatusFilter,handleDeliveryStatusFilterChange}:Props) =>{

    const handleSelectChange = (event: SelectChangeEvent) => {
        handleDeliveryStatusFilterChange(event.target.value)
      }
    
    return(
        <>
        <FormControl sx={{ m: 1, minWidth: 300}}>
            <Select
            displayEmpty
            sx={{
                borderRadius:"10px",
                '& .MuiSelect-select': { // 調整內部的 select 元素
                padding: '9.5px 14px', // 覆蓋內部選擇框的 padding
                }
            }}
            value={deliveryStatusFilter}
            onChange={handleSelectChange}
            >
          <MenuItem value="">All</MenuItem>
          <MenuItem value="delivered">Delivered</MenuItem>
          <MenuItem value="pending">Pending</MenuItem>
          <MenuItem value="cancelled">Cancelled</MenuItem>
        </Select>
      </FormControl>
        </>
    )
}

export default DNDeliveryNoteStatus