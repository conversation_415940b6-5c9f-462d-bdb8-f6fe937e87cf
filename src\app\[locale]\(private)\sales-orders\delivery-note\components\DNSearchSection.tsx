import { Box, TextField } from "@mui/material"

type Props = {
    deliveryNoteFilter:string
    handleDeliveryNoteFilterChange:(deliveryNoteFilter:string)=>void
}

const DNSearchSection = ( {deliveryNoteFilter,handleDeliveryNoteFilterChange}:Props) =>{

    const handleTextFieldChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>{
        handleDeliveryNoteFilterChange(event.target.value);
      }

    return(
        <>
        <Box>
        <TextField
          id="outlined-basic"
          label="Search"
          variant="outlined"
          value={deliveryNoteFilter}
          onChange={handleTextFieldChange}
        />
      </Box>
        </>
    )
}

export default DNSearchSection