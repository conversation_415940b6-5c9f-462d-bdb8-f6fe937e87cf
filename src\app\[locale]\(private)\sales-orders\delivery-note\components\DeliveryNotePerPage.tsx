import { FormControl, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";

type Props = {
    deliveryNotePerPage:number,
    handleDeliveryNotePage:(deliveryNotePerPage:number)=>void
}

const DeliveryNotePerPage = ({deliveryNotePerPage,handleDeliveryNotePage}:Props) =>{

    const handleDeliveryNotePerPageFilterChange = (event: SelectChangeEvent<number>) =>{
        const value = Number(event.target.value);
        handleDeliveryNotePage(value);
    }
    return(
        <>
        <Typography>Show Per Page:</Typography>&nbsp;&nbsp;
              <FormControl variant="outlined" style={{
        width: '200px'
      }}
      >
        <Select
            sx={{
            borderRadius:"10px",
            '& .MuiSelect-select': { // 調整內部的 select 元素
                padding: '9.5px 14px', // 覆蓋內部選擇框的 padding
            }
            }}
          value={deliveryNotePerPage}
          onChange={handleDeliveryNotePerPageFilterChange}
        >
          <MenuItem value={10}>10</MenuItem>
          <MenuItem value={20}>20</MenuItem>
          <MenuItem value={30}>30</MenuItem>
        </Select>
      </FormControl>
        </>
    )
}

export default DeliveryNotePerPage; 