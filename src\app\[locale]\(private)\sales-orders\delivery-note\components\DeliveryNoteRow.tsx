import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import React from "react";
import {Checkbox, IconButton} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import {useRouter} from "next/navigation";
import { AllSalesOrder } from "@/interface/ISalesOrderDto";
import { format } from 'date-fns';

type Props = {
  selectedDeliveryNoteId:string,
  handleCheckBoxChange:(selectedDeliveryNoteId:string)=>void
  allSalesOrder:AllSalesOrder
}


const DeliveryNoteRow = ({selectedDeliveryNoteId,handleCheckBoxChange,allSalesOrder}:Props) =>{
  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

  console.log("check decode value",allSalesOrder.deliveryNoteId)
  
  const router = useRouter();
  const handleQueryClick = () => {
    router.push(`/sales-orders/delivery-note/${allSalesOrder.deliveryNoteId}`)
  }

  const handleCheckBoxStatusChange = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) =>{
    if(event.target.checked){
       handleCheckBoxChange(allSalesOrder.deliveryNoteId || "");
    }else{
      handleCheckBoxChange("");
    }
  }
  const createdAtformattedDate = format(new Date(allSalesOrder.createdAt * 1000), "yyyy-MM-dd HH:mm:ss");
  const updatedAtdAtformattedDate = format(new Date(allSalesOrder.updatedAt * 1000), "yyyy-MM-dd HH:mm:ss");
  return(
    <>
      <TableRow>
        <TableCell><Checkbox 
          checked={selectedDeliveryNoteId === allSalesOrder.deliveryNoteId}
          onChange={handleCheckBoxStatusChange}
        />
        </TableCell>
        <TableCell><IconButton sx={{
          background: "#24378C",
          color:"white",
          '&:hover': {
            background: "#24378C", // 懸停時的背景顏色保持不變
          },
        }}
         onClick={handleQueryClick}
        >
          <SearchIcon/>
        </IconButton></TableCell>
        <TableCell>{allSalesOrder.deliveryNoteId}</TableCell>
        <TableCell>{allSalesOrder.orderNo}</TableCell>
        <TableCell>{allSalesOrder.clientName}</TableCell>
        <TableCell>{allSalesOrder.clientEmail}</TableCell>
        <TableCell>{allSalesOrder.deliveryStatus}</TableCell>
        <TableCell>{allSalesOrder.handlingStaff}</TableCell>
        <TableCell>{allSalesOrder.remarks}</TableCell>
        <TableCell>{updatedAtdAtformattedDate}</TableCell>
        <TableCell>{createdAtformattedDate}</TableCell>
      </TableRow>
    </>
  )
}

export default DeliveryNoteRow