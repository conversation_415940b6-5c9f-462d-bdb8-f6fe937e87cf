"use client";
import React, { useEffect } from "react";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import {Box, Button, Checkbox, Pagination, Stack} from "@mui/material";
import DeliveryNoteRow from "@/app/[locale]/(private)/sales-orders/delivery-note/components/DeliveryNoteRow";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import { useState } from 'react';
import { SalesOrderDto } from "@/interface/ISalesOrderDto";
import AddUpdateProductSuccessSnackBar from "@/components/AddUpdateProductSuccessSnackBar";
import xior from "xior";
import {showErrorPopUp} from "@/utils/toast";

type Props = {
  getSalesOrderDtoList:SalesOrderDto,
  deliveryNoteFilter:string
  deliveryStatusFilter:string
  fetchSalesOrders: () => Promise<void>
  page:number,
  handlePageChange:(page:number)=>void,
  deliveryNotePerPage:number,
}

const DeliveryNoteTable = ({getSalesOrderDtoList,
                            fetchSalesOrders,
                            deliveryNoteFilter,
                            deliveryStatusFilter,
                            page,
                            deliveryNotePerPage,
                            handlePageChange
                          }:Props) =>{

  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
  const [selectedDeliveryNoteId, setSelectedDeliveryNoteId] = useState<string>("");
  const [snackbarOpen,setSnackbarOpen] = useState<boolean>(false);
  const {showToast} = showErrorPopUp();

  const handleCheckBoxChange = (selectedDeliveryNoteId:string)=>{
    setSelectedDeliveryNoteId(selectedDeliveryNoteId);
    console.log("Selected Delivery Note ID:", selectedDeliveryNoteId);
  }

  // const updateDeliveryStatus = async () =>{
  //     await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/sales-orders/updateDeliveryStatus/${selectedDeliveryNoteId}`, {
  //     method: 'PUT',
  //     headers: {
  //       'Content-Type': 'application/json',
  //   }, 
  // });
  //     setSnackbarOpen(true);
  //     await fetchSalesOrders();
  // }

  const updateDeliveryStatus = async () =>{
    try {
 
      const requestBody = {}; 

      await xior.put(`/api/sales-orders/updateDeliveryStatus/${selectedDeliveryNoteId}`, requestBody);
      // setSnackbarOpen(true);
      showToast('Successfully Updated');

      await fetchSalesOrders();
  } catch (error) {
      console.error('Error updating delivery status:', error);
      showToast('update delivery status failed');
  }
  }

  const handleSnackbarClose = () =>{
    setSnackbarOpen(false)
  }

  const handlePaginationChange = (_event: React.ChangeEvent<unknown>, value: number) =>{
    handlePageChange(value);
    }

const startIndex = (page - 1) * deliveryNotePerPage;

const filteredSalesOrders = getSalesOrderDtoList.allSalesOrders.filter(value => 
  value !== null && 
  value !== undefined && 
  value.deliveryNoteId && // 確保 deliveryNoteId 存在
  (
    value.deliveryNoteId.toLowerCase().includes(deliveryNoteFilter.toLowerCase()) ||
    value.orderNo.toLowerCase().includes(deliveryNoteFilter.toLowerCase()) ||
    value.clientName.toLowerCase().includes(deliveryNoteFilter.toLowerCase()) ||
    value.clientEmail.toLowerCase().includes(deliveryNoteFilter.toLowerCase()) ||
    value.handlingStaff?.toLowerCase().includes(deliveryNoteFilter.toLowerCase()) ||
    value.remarks?.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
  ) &&
  (value.deliveryStatus === null || value.deliveryStatus.includes(deliveryStatusFilter))
);

const currentSalesOrderDtoListDtoList = filteredSalesOrders.slice(startIndex, startIndex + deliveryNotePerPage);
console.log('Total Records:', getSalesOrderDtoList.allSalesOrders.length);
console.log('Start Index:', startIndex);
console.log('Current Records:', currentSalesOrderDtoListDtoList);

  return(
    <>
      <Table sx={{ minWidth: 650 }} aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell></TableCell>
            <TableCell>Delivery Note ID</TableCell>
            <TableCell>Order Number</TableCell>
            <TableCell>Recipient Name</TableCell>
            <TableCell>Recipient Email</TableCell>
            <TableCell>Delivery Status</TableCell>
            <TableCell>Handling Staff</TableCell>
            <TableCell>Remark</TableCell>
            <TableCell>Updated At</TableCell>
            <TableCell>Created At</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {
            currentSalesOrderDtoListDtoList.filter((value)=>(
              value !== null && value !== undefined && value.deliveryNoteId
              && 
              (value.deliveryNoteId.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
               || value.orderNo.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
               || value.clientName.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
               || value.clientEmail.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
               || value.handlingStaff?.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
               || value.remarks?.toLowerCase().includes(deliveryNoteFilter.toLowerCase())
              )
              &&
              (value.deliveryStatus.includes(deliveryStatusFilter))
            ))
            .map((value)=>(
              <DeliveryNoteRow 
              key={value.deliveryNoteId} allSalesOrder={value}
              selectedDeliveryNoteId={selectedDeliveryNoteId}
              handleCheckBoxChange={handleCheckBoxChange}
              />
            ))
          }
        </TableBody>
      </Table>
      <Button variant="contained" sx={{
        mt:"15px"
      }}
      onClick={updateDeliveryStatus}
      >
        Delivered
      </Button>
      <AddUpdateProductSuccessSnackBar open={snackbarOpen} handleClose={handleSnackbarClose}/>
      <Box sx={{
                    display:"flex",
                    alignItems:"center",
                    flexDirection:"column",
                    mt:"10px"
                }}>
                <Stack spacing={2}>
                <Pagination
                    count={Math.ceil(filteredSalesOrders.length / deliveryNotePerPage)}
                    page={page}
                    onChange={handlePaginationChange}
                    variant="outlined"
                    shape="rounded"
                    />
                </Stack>
      </Box>
    </>
  )
}

export default DeliveryNoteTable;