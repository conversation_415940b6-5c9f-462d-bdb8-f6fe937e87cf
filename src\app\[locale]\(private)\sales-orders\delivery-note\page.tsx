"use client";
import type { NextPage } from "next";
import {Box} from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import DeliveryNoteTable from "@/app/[locale]/(private)/sales-orders/delivery-note/components/DeliveryNoteTable";
import {useEffect, useState} from "react";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import mockData from "../response.json"
import { SalesOrderDto } from "@/interface/ISalesOrderDto";
import DNSearchSection from "./components/DNSearchSection";
import DNDeliveryNoteStatus from "./components/DNDeliveryNoteStatus";
import DeliveryNotePerPage from "./components/DeliveryNotePerPage";
import xior from "xior";

const DeliveryNote: NextPage = () => {
  const t = useTranslations("sales_orders");
  // const [salesOrderDtoList,setSalesOrderDtoList] = useState<ISalesOrderDto[] | undefined>(undefined);
  const [getSalesOrderDtoList,setGetSalesOrderDtoList] = useState<SalesOrderDto | undefined> (undefined);
  const [deliveryNoteFilter,setDeliveryNoteFilter] = useState<string>("");
  const [deliveryStatusFilter,setDeliveryStatusFilter,] = useState<string>("");
  const [page,setPage] = useState<number>(1);
  const [deliveryNotePerPage,setDeliveryNotePage] = useState<number>(10);
  

  const handlePageChange = (page:number) =>{
    setPage(page);
  }

  const handleDeliveryNotePerPage = (orderPerPage:number) =>{
    setDeliveryNotePage(orderPerPage);
    setPage(1);
  }

  const handleDeliveryNoteFilterChange = (deliveryNoteFilter:string) =>{
    setDeliveryNoteFilter(deliveryNoteFilter)
  }

  const handleDeliveryStatusFilterChange = (deliveryStatusFilter:string) => {
    setDeliveryStatusFilter(deliveryStatusFilter)
  }

  
  const fetchSalesOrders = async () => {
    const response = await xior.get('/api/sales-orders');
    setGetSalesOrderDtoList(response.data);
};

  useEffect(() => {
    fetchSalesOrders()
  }, []);

  return (
    <Box sx={{ height: "100%" }} display={"flex"} flexDirection={"column"}>
      <PageHeader title={t("delivery_note")}>
        <>
          <ExportButton />
        </>
      </PageHeader>
        <Box flex={1} padding="26px 34px" >
          <Box sx={{
              display:"flex",
              alignItems:"center",
              mb:"8px"
            }}>
            <DNSearchSection 
              deliveryNoteFilter={deliveryNoteFilter}
              handleDeliveryNoteFilterChange={handleDeliveryNoteFilterChange}
            />
            <DNDeliveryNoteStatus deliveryStatusFilter={deliveryStatusFilter} handleDeliveryStatusFilterChange={handleDeliveryStatusFilterChange}/>
            <DeliveryNotePerPage deliveryNotePerPage={deliveryNotePerPage} handleDeliveryNotePage={handleDeliveryNotePerPage}/>
          </Box>
          {
            getSalesOrderDtoList &&
            <DeliveryNoteTable 
            getSalesOrderDtoList={getSalesOrderDtoList} 
            fetchSalesOrders={fetchSalesOrders}
            deliveryNoteFilter={deliveryNoteFilter}
            deliveryStatusFilter={deliveryStatusFilter}
            page={page}
            deliveryNotePerPage={deliveryNotePerPage}
            handlePageChange={handlePageChange}
            />
          }
        </Box>
    </Box>
  );
};

export default DeliveryNote;