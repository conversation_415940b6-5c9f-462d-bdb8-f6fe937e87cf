"use client";
import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select, { SelectChangeEvent } from '@mui/material/Select';

type Props = {
  deliveryStatusFilter:string,
  handleDeliveryStatusFilterChange:(deliveryStatusFilter:string)=> void
}

const DeliveryStatusFilter = ({deliveryStatusFilter,
                               handleDeliveryStatusFilterChange
                               }:Props) => {

  const handleSelectChange = (event: SelectChangeEvent) => {
    handleDeliveryStatusFilterChange(event.target.value)
  }

  return (
    <div>
      <FormControl sx={{ m: 1, minWidth: 300}}>
        <Select
          displayEmpty
          sx={{
            borderRadius:"10px",
            '& .MuiSelect-select': { // 調整內部的 select 元素
              padding: '9.5px 14px', // 覆蓋內部選擇框的 padding
            }
          }}
          value={deliveryStatusFilter}
          onChange={handleSelectChange}
        >
          <MenuItem value="">All</MenuItem>
          <MenuItem value="delivered">Delivered</MenuItem>
          <MenuItem value="pending">Pending</MenuItem>
          <MenuItem value="cancelled">Cancelled</MenuItem>
        </Select>
      </FormControl>
    </div>
  );
}
export default DeliveryStatusFilter