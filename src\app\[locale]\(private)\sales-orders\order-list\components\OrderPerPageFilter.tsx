"use client";
import { FormControl, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material"

type Props = {
    orderPerPage:number,
    handleOrderPerPage:(orderPerPage:number)=>void
}

const OrderPerPageFilter = ({orderPerPage,handleOrderPerPage}:Props) =>{

    const handleOrderPerPageFilterChange = (event: SelectChangeEvent<number>) =>{
        const value = Number(event.target.value);
        handleOrderPerPage(value);
    }

    return(
        <>
        <Typography>Show Per Page:</Typography>&nbsp;&nbsp;
              <FormControl variant="outlined" style={{
        width: '200px'
      }}
      >
        <Select
            sx={{
            borderRadius:"10px",
            '& .MuiSelect-select': { 
                padding: '9.5px 14px', 
            }
            }}
          value={orderPerPage}
          onChange={handleOrderPerPageFilterChange}
        >
          <MenuItem value={10}>10</MenuItem>
          <MenuItem value={20}>20</MenuItem>
          <MenuItem value={30}>30</MenuItem>
        </Select>
      </FormControl>
        </>
    )
}

export default OrderPerPageFilter