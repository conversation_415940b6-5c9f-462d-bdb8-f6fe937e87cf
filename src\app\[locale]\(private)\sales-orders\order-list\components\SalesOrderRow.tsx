import {Icon<PERSON>utton, TableCell, TableRow} from "@mui/material";
import {ISalesOrderDto} from "@/interface/ISalesOrder";
import SearchIcon from '@mui/icons-material/Search';
import { useRouter } from 'next/navigation';
import { AllSalesOrder } from "@/interface/ISalesOrderDto";
import { format } from 'date-fns';

type Props = {
  allSalesOrder: AllSalesOrder
}

const SalesOrderRow = ({allSalesOrder}:Props) => {

  const router = useRouter();

  const handleQueryClick = () => {
    router.push(`/sales-orders/${allSalesOrder.orderNo}`)
  }

  const renderDeliveryNoteIdExist = () => {
    if(!allSalesOrder.deliveryNoteId){
      return(
        <>
          <TableCell>-</TableCell>
        </>
      )
    }else{
      return (
        <>
          <TableCell>{allSalesOrder.deliveryNoteId}</TableCell>
        </>
      )
    }
  }

  const createdAtformattedDate = format(new Date(allSalesOrder.createdAt * 1000), "yyyy-MM-dd HH:mm:ss");

  return(
    <>
        <TableRow>
          <TableCell>
              <IconButton sx={{
                background: "#24378C",
                color:"white",
                '&:hover': {
                  background: "#24378C", 
              },
              }}
              onClick={handleQueryClick}
              >
                <SearchIcon/>
              </IconButton>
        
          </TableCell>
          <TableCell>{allSalesOrder.orderNo}</TableCell>
          <TableCell>{allSalesOrder.clientName}</TableCell>
          <TableCell>{allSalesOrder.clientEmail}</TableCell>
          <TableCell>${allSalesOrder.total ? allSalesOrder.total.toLocaleString() : '0.00'}</TableCell>
          <TableCell>{allSalesOrder.deliveryStatus? allSalesOrder.deliveryStatus: "-"}</TableCell>
          {
            renderDeliveryNoteIdExist()
          }
          <TableCell>{createdAtformattedDate}</TableCell>
        </TableRow>
    </>
  )
}

export default SalesOrderRow