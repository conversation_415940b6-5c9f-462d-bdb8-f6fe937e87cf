"use client";
import {<PERSON>, Stack, TableBody, TableCell, TableHead, TableRow} from "@mui/material";
import Table from "@mui/material/Table";
import SalesOrderRow from "@/app/[locale]/(private)/sales-orders/order-list/components/SalesOrderRow";
import {SalesOrderDto} from "@/interface/ISalesOrderDto"
import Pagination from '@mui/material/Pagination';

type Props ={
    // iSalesOrderDtoList:ISalesOrderDto[],
    salesOrderFilter:string,
    deliveryStatusFilter:string,
    getSalesOrderList: SalesOrderDto,
    page:number,
    handlePageChange:(page:number)=>void,
    orderPerPage:number,
    // handleOrderPerPage:(orderPerPage:number)=>void
}

const SalesOrderTable = ({
                          deliveryStatusFilter,
                          salesOrderFilter,
                          getSalesOrderList,
                          page,
                          orderPerPage,
                          handlePageChange,
                       }:Props) =>{



    const handlePaginationChange = (_event: React.ChangeEvent<unknown>, value: number) =>{
        handlePageChange(value);
        }

    const startIndex = (page - 1) * orderPerPage;

    const filteredSalesOrders = getSalesOrderList.allSalesOrders.filter(value => 
        (
          (value.orderNo && value.orderNo.toLowerCase().includes(salesOrderFilter.toLowerCase())) ||
          (value.clientName && value.clientName.toLowerCase().includes(salesOrderFilter.toLowerCase())) ||
          (value.clientEmail && value.clientEmail.toLowerCase().includes(salesOrderFilter.toLowerCase())) ||
          (value.total && value.total.toString() === salesOrderFilter) ||
          (value.deliveryNoteId && value.deliveryNoteId.toLowerCase().includes(salesOrderFilter.toLowerCase()))
        ) &&
        (value.deliveryStatus === null || value.deliveryStatus.includes(deliveryStatusFilter))
    );
    
    const currentSalesOrderDtoList = filteredSalesOrders.slice(startIndex, startIndex + orderPerPage);
    return (
        <>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell></TableCell>
                            <TableCell>Order Number</TableCell>
                            <TableCell>Client Name</TableCell>
                            <TableCell>Client Email</TableCell>
                            <TableCell>Total(USD)</TableCell>
                            <TableCell>Delivery Status</TableCell>
                            <TableCell>Delivery Note ID</TableCell>
                            <TableCell>Created At</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {/* {
                            getSalesOrderList.filter((value)=>(
                              (value.orderId.toLowerCase().includes(salesOrderFilter.toLowerCase())
                              || value.clientName.toLowerCase().includes(salesOrderFilter.toLowerCase())
                              || value.clientEmail.toLowerCase().includes(salesOrderFilter.toLowerCase())
                              || value.Total.toString() === (salesOrderFilter)
                              || value.deliveryNoteId.toLowerCase().includes(salesOrderFilter.toLowerCase()))
                               && value.deliveryStatus.includes(deliveryStatusFilter)
                            ))
                              .map((value) => (
                              <SalesOrderRow key={value.orderId} iSalesOrderDto={value}/>
                            ))

                        } */}
                        {
                            currentSalesOrderDtoList &&
                            // <Testing salesOrderlist={getSalesOrderList}/>
                            currentSalesOrderDtoList.filter((value)=>(
                            (value.orderNo.toLowerCase().includes(salesOrderFilter.toLowerCase())
                            || value.clientName.toLowerCase().includes(salesOrderFilter.toLowerCase())
                            || value.clientEmail.toLowerCase().includes(salesOrderFilter.toLowerCase())
                            || value.total.toString() === (salesOrderFilter)
                            || value.deliveryNoteId?.toLowerCase().includes(salesOrderFilter.toLowerCase()))
                            || value.createdAt.toString() === (salesOrderFilter)
                                && value.deliveryStatus.includes(deliveryStatusFilter)
                                && value.transactionStatus.includes("success")
                            ))
                            .map((value)=>(
                            <SalesOrderRow key={value.id} allSalesOrder={value}/>
                            ))
                        }
                    </TableBody>
                </Table>
                <Box sx={{
                    display:"flex",
                    alignItems:"center",
                    flexDirection:"column",
                    mt:"10px"
                }}>
                <Stack spacing={2}>
                <Pagination
                    count={Math.ceil(filteredSalesOrders.length / orderPerPage)}
                    page={page}
                    onChange={handlePaginationChange}
                    variant="outlined"
                    shape="rounded"
                    />
                </Stack>
                </Box>
        </>
    )
}

export default SalesOrderTable;