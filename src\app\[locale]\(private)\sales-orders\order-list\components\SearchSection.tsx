import {Box, TextField, } from "@mui/material";

type Props = {
  salesOrderFilter:string,
  handleSalesOrderFilterChange:(salesOrderFilter:string)=> void
}

const SearchSection = ({salesOrderFilter,
                       handleSalesOrderFilterChange
                       }:Props) => {

  const handleTextFieldChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>{
    handleSalesOrderFilterChange(event.target.value);
  }

  return(
    <>
      <Box>
        <TextField
          id="outlined-basic"
          label="Search"
          variant="outlined"
          value={salesOrderFilter}
          onChange={handleTextFieldChange}
        />
      </Box>
    </>
  )
}

export default SearchSection