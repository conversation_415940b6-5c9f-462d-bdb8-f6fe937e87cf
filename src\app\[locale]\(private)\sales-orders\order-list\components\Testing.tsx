// "use client";
// import {AllSalesOrder} from "@/interface/ISalesOrderDto"
// import { Typography } from "@mui/material";


// type Props = {
//     allSalesOrder: AllSalesOrder
// }

// const Testing = ({allSalesOrder}:Props) =>{
//     return (
//         <>
//         <Typography>
//         {allSalesOrder.currency}
//         {allSalesOrder.description}
//         {allSalesOrder.amount}
//         {allSalesOrder.orderNo}
// </Typography>
//         </>
//     )
// }

// export default Testing;