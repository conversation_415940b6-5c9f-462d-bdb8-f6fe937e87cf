"use client";
import type { NextPage } from "next";
import { Box} from "@mui/material";
import { useTranslations } from "next-intl";
import PageHeader from "@/components/PageHeader";
import ExportButton from "@/components/buttons/ExportButton";
import SalesOrderTable from "@/app/[locale]/(private)/sales-orders/order-list/components/SalesOrdersTable";
import { useEffect, useState } from "react"; // 使用 useEffect 替代 useLayoutEffect
import { ISalesOrderDto } from "@/interface/ISalesOrder";
import DeliveryStatusFilter from "@/app/[locale]/(private)/sales-orders/order-list/components/DeliveryStatusFilter";
import SearchSection from "@/app/[locale]/(private)/sales-orders/order-list/components/SearchSection";
import { SalesOrderDto } from "@/interface/ISalesOrderDto";
import OrderPerPageFilter from "./components/OrderPerPageFilter";
import xior from "xior";


const SalesOrder: NextPage = () => {
  const t = useTranslations("sales_orders");

  // const [salesOrderDtoList, setSalesOrderDtoList] = useState<ISalesOrderDto[] | undefined>(undefined);
  const [salesOrderFilter,setSalesOrderFilter] = useState<string>("");
  const [deliveryStatusFilter,setDeliveryStatusFilter,] = useState<string>("");
  const [getSalesOrderList,setSalesOrderList] = useState<SalesOrderDto | undefined>(undefined);
  const [page,setPage] = useState<number>(1);
  const [orderPerPage,setOrderPage] = useState<number>(10);

  const handlePageChange = (page:number) =>{
    setPage(page);
  }

  const handleOrderPerPage = (orderPerPage:number) =>{
    setOrderPage(orderPerPage);
    setPage(1);
  }

  const handleSalesOrderFilterChange = (salesOrderFilter:string) =>{
    setSalesOrderFilter(salesOrderFilter)
  }

  const fetchSalesOrders = async () =>{
      const response = await xior.get('/api/sales-orders');
      setSalesOrderList(response.data);

  }

  const handleDeliveryStatusFilterChange = (deliveryStatusFilter:string) => {
    setDeliveryStatusFilter(deliveryStatusFilter)
  }

  useEffect(() => {
    fetchSalesOrders()
  }, []);

  return (
    <>
      <div suppressHydrationWarning={true}>
        <Box sx={{ height: "100%" }}>
          <PageHeader title={t("orders_list")}>
            <ExportButton />
          </PageHeader>
          <Box flex={1} padding="26px 34px">
            <Box sx={{
              display:"flex",
              alignItems:"center",
              mb:"8px"
            }}>
              <SearchSection
                salesOrderFilter={salesOrderFilter}
                handleSalesOrderFilterChange={handleSalesOrderFilterChange}
              />
              <DeliveryStatusFilter
              deliveryStatusFilter={deliveryStatusFilter}
              handleDeliveryStatusFilterChange={handleDeliveryStatusFilterChange}
              />
              <OrderPerPageFilter 
              orderPerPage={orderPerPage} 
              handleOrderPerPage={handleOrderPerPage}/>
            </Box>
            {getSalesOrderList &&
              <SalesOrderTable
                  // iSalesOrderDtoList={salesOrderDtoList}
                  salesOrderFilter={salesOrderFilter}
                  deliveryStatusFilter={deliveryStatusFilter}
                  getSalesOrderList={getSalesOrderList}
                  page={page}
                  orderPerPage={orderPerPage}
                  handlePageChange={handlePageChange}
                  // handleOrderPerPage={handleOrderPerPage}
              />
            }
            {/* {
              getSalesOrderList &&
                // <Testing salesOrderlist={getSalesOrderList}/>
                getSalesOrderList?.allSalesOrders.map((value)=>(
                  <Testing key={value.id} allSalesOrder={value}/>
                ))
            } */}
    
          </Box>
        </Box>
      </div>
    </>
  );
};

export default SalesOrder;