"use client"
import { Accordion, AccordionDetails, AccordionSummary, Box, Button, InputAdornment, Link, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { styled } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import Divider from '@mui/material/Divider';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {ProductDetailDto} from "@/interface/IProductDetailDto"
import { IPaymentCreateDto } from '@/interface/IPaymentGateway';
import { IPromoCode } from "@/interface/IPromoCode";
import xior from "xior";
import { IProfile } from "@/interface/IProfile";
import { IPromoCodeRecords } from "@/interface/IPromoCodeRecords";
import { UpdateAmount } from "@/interface/IPromoCodeRecords"
import { ICheckOutDto } from "@/interface/ICheckOutDto";
type Props = {
    paymentCreateDto: IPaymentCreateDto;
    handlePaymentDto:(paymentCreateDto:IPaymentCreateDto)=> void
    checkoutDto: ICheckOutDto
    // buyQuantity: number
    handleFinalAmountChange:(finalAmount:number)=> void
    handlePromoCodeChange:(getPromoCode:string)=> void
}

const OrderSection = ({paymentCreateDto,checkoutDto,handlePaymentDto,handleFinalAmountChange,handlePromoCodeChange}:Props) =>{

    const [productDetailDto, setProductDetailDto] = useState<ProductDetailDto | undefined>(undefined);
    const [inputValue, setInputValue] = useState<string>('');
    const [showIcon, setShowIcon] = useState<boolean>(false);
    const [promoCode,setPromoCode]= useState<IPromoCode| undefined>(undefined);
    const [profile,setProfile] = useState<IProfile|undefined>(undefined);
    const [promoCodeRecords, setPromoCodeRecords] = useState<IPromoCodeRecords | undefined>(undefined);
    const [updateAmount, setUpdateAmount] = useState<number>(0);
 
    const fetchProductDetails = async () =>{

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/products/${checkoutDto.order.product.id}`,{
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
        }, 
        });
        const data = await response.json();
        setProductDetailDto(data);
      }

    // const promoId = 1;  
     
    // const fetchPromoCode = async () =>{
    //     try {
    //         const response = await xior.get(`/api/promocode/${promoId}`);
    //         setPromoCode(response.data);
    //     } catch (error) {
    //         console.error('Error fetching promo code:', error);
    //     }
    // }

    // const fetchUserProfile = async () =>{
    //     const response = await xior.get('/api/profile');
    //     setProfile(response.data);
    // }

    const handlePostPromoCodeRecords = async () => {
        if (promoCode && checkoutDto.order.user.email) {
            const promoCodeRecords: IPromoCodeRecords = {
                promoCodeId: promoCode.id,
                userEmail: checkoutDto.order.user.email,
            };
    
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/promocode/records`, {
                    method: 'POST', 
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(promoCodeRecords), 
                });
    
                if (!response.ok) {
            
                    const errorData = await response.json();
                    console.error('Error posting promo code:', errorData);
                    throw new Error(errorData.error || 'Something went wrong');
                }
    
                const data = await response.json(); 
                console.log('Promo code records posted successfully:', data);
            } catch (error) {
                console.error('Error posting promo code:', error);
            }
        } else {
            console.error('Promo code or profile is missing');
        }
    };

    // console.log(promoCode);
    // console.log(profile);

    const updateAmountApi = async () =>{
        try{

            const requestBody = {};

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/promocode/calculate/${promoCode?.id}/${subtotal}`, {
                method: 'POST', 
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody), 
            });

            const data = await response.json();
            setUpdateAmount(data)

        }catch(error){
            console.error('Error updating amount:', error);
        }
    }


    const Item = styled('div')(({ theme }) => ({
        backgroundColor: '#fff',
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: 'center',
        color: theme.palette.text.secondary,
        ...theme.applyStyles('dark', {
          backgroundColor: '#1A2027',
        }),
      }));

      const itemStylesRight = {
        color:"black",
        display:"flex",
        backgroundColor:"rgba(246, 249, 255, 1)"
      }
      
      const itemStylesLeft = {
        display:"flex",
        backgroundColor:"rgba(246, 249, 255, 1)"
      }

      

      const handleInputChange = (event:React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setInputValue(event.target.value);
   
      };
    
      const handleSubmit = () => {
        if(inputValue.includes(promoCode!.promoCode)){
            setShowIcon(true);
            // handlePostPromoCodeRecords();
            // updateAmountApi();
        }else{
            setShowIcon(false);
            
        }

      };

      const handlePromoCodeRemove = () =>{
        setInputValue('');  
        setShowIcon(false); 
      }

      const promocodesection = () =>{

        return(
            <>
            <Grid container spacing={2}>
            <Grid item xs={8}>
                <Box >
                <TextField
                    label="PromoCode"
                    variant="outlined"
                    value={inputValue}
                    onChange={handleInputChange}
                    disabled={showIcon}
                    margin="normal"
                    InputProps={{
                        endAdornment: showIcon ? (
                          <InputAdornment position="end">
                            <CheckCircleOutlineIcon sx={{
                                color:"rgba(195, 214, 82, 1)"
                            }}/>
                          </InputAdornment>
                        ) : null,
                      }}
                />
            </Box>
            </Grid>
            <Grid item xs={4}>
                <Box sx={{
                    mt:"17px"
                }}>
                    {
                        renderAppluBTN()
                    }
                </Box>
            </Grid>
            </Grid>
            </>
        )
      }


      const renderAppluBTN = () =>{
        if(showIcon){
            return(
                <>
                <Button variant="contained" color="primary" onClick={handlePromoCodeRemove}>
                    Remove
                </Button>
                </>
            )
        }else{
            return(
                <>
              <Button variant="contained" color="primary"     onClick={() => {
                    handleSubmit(); 
                }}>
                Apply
            </Button>
                </>
            )
        }
      }

      const renderDisplayPromoCodeItemList = () =>{
        if(showIcon){
            return(
                <>
                <Grid item xs={8}>
                        <Item sx={itemStylesLeft}>
                        <Typography>Promo Code: {promoCode!.promoCode}</Typography>
                        </Item>
                </Grid>
                <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography>-${promoCode!.discountAmount}</Typography>
                        </Item>
                </Grid>
                </>
            )
        }else if(!showIcon){
            return(
                <>
                </>
            )
        }
      }
      const quantity = checkoutDto?.order.quantity;
      const price = Number(productDetailDto?.price) || 0; 
      const subtotal = price * quantity; 
      const getPromoCode = showIcon ? promoCode?.promoCode : null;

    function calculateFinalAmount(subtotal:number, showIcon:boolean) {
        const finalAmount = showIcon ? updateAmount : subtotal;
        return finalAmount;
    }

    const finalAmount = calculateFinalAmount(subtotal, showIcon);


    // console.log("Check call API updateAmount", updateAmount)
    // console.log("Check call API finalAmount" , finalAmount)

        useEffect(() => {
            handleFinalAmountChange(finalAmount);
        }, [finalAmount]);

        useEffect(() => {
                  if(getPromoCode){
                    handlePromoCodeChange(getPromoCode)
                }
        }, [getPromoCode]);

      useEffect(() => {
        fetchProductDetails();
      }, []);

      useEffect(() => {
        if (productDetailDto) {

          const updatedPaymentCreateDto: IPaymentCreateDto = {
            ...paymentCreateDto, 
            feeType: productDetailDto.currency, 
            totalFee:finalAmount,
            promoCode: getPromoCode,
            description:productDetailDto.description,
            product: [
                {
                  id: productDetailDto.id, 
                  quantity: quantity, 
                },
                ...paymentCreateDto.product 
              ],
        };
    
          handlePaymentDto(updatedPaymentCreateDto);
        }
      }, [productDetailDto]); 

    return(
        <>
        <Box 
        sx={{
            backgroundColor:"rgba(246, 249, 255, 1)"
        }}>
            <Box>
                <Grid container spacing={2} sx={{
                    mt:"2px"
                }}>
                    <Grid item xs={8}>
                        <Item sx={itemStylesRight}>
                            <Typography variant="h6">Order Summary</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                            <Box
                            sx={{
                                display: {
                                xs: 'none',  // 在移动设备上隐藏
                                sm: 'flex', // 在 PC 上显示
                                },
                                alignItems: 'center',
                            }}
                            >
                            <ShoppingCartIcon /> &nbsp;&nbsp;&nbsp;
                            <Typography variant="subtitle1">1</Typography>&nbsp;&nbsp;&nbsp;
                            <Typography variant="subtitle1">item</Typography>
                            </Box>
                        </Item>
                    </Grid>
                    <Grid item xs={8}>
                        <Item sx={itemStylesRight}>
                            <img src={productDetailDto?.thumbnail}
                                width="100px"/>
                        <Box 
                        textAlign="left"
                        sx={{
                            ml:"10px"
                        }}>
                        <Typography variant="body1">{productDetailDto?.name}</Typography>
                        <Typography variant="body1">${price}</Typography>
                        </Box>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography variant="subtitle1">x</Typography> &nbsp;&nbsp;&nbsp;
                            <Typography variant="subtitle1">{quantity}</Typography>
                        
                        </Item>
                    </Grid>
                </Grid>
                <Divider sx={{
                    mt:"10px"
                }}/>
                 {/* {
                    promocodesection()
                }  */}

                <Grid container spacing={1} sx={{
                    mt:"2px"
                    }}>
                    <Grid item xs={8}>
                        <Item sx={itemStylesLeft}>
                            <Typography>Subtotal</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography>${subtotal}</Typography>
                        </Item>
                    </Grid>
                    {/* <Grid item xs={8}>
                        <Item sx={itemStylesLeft}>
                        <Typography>Discount</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography>-$10</Typography>
                        </Item>
                    </Grid> */}
                    {
                        renderDisplayPromoCodeItemList()
                    }
                    {/* <Grid item xs={8}>
                        <Item sx={itemStylesLeft}>
                        <Typography>Tax</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography>$0</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={8}>
                        <Item sx={itemStylesLeft}>
                        <Typography>Shipping fee</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography>Calculated at next step</Typography>
                        </Item>
                    </Grid> */}
                    <Grid item xs={8}>
                        <Item sx={itemStylesRight}>
                        <Typography>Total</Typography>
                        </Item>
                    </Grid>
                    <Grid item xs={4}>
                        <Item sx={itemStylesRight}>
                        <Typography variant="h6">HKD${finalAmount}</Typography>
                        </Item>
                    </Grid>
                </Grid>
                <Box sx={{
                    mr:"20px",
                    mb:"20px",
                    mt:"5px"
                }}>
                <Accordion>
                    <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                    >
                    Payment Policy
                    </AccordionSummary>
                    <AccordionDetails>
                    Please return to <Link href="https://fun.gbgt.tv" target="_blank" rel="noopener noreferrer">https://fun.gbgt.tv</Link> to view the payment policy.
                    </AccordionDetails>
                </Accordion>
                <Accordion>
                    <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel2-content"
                    id="panel2-header"
                    >
                    Returns Policy
                    </AccordionSummary>
                    <AccordionDetails>
                    Please return to <Link href="https://fun.gbgt.tv" target="_blank" rel="noopener noreferrer">https://fun.gbgt.tv</Link> to view the return policy.
                    </AccordionDetails>
                </Accordion>
                </Box>
            </Box>
        </Box>
        </>
    )
}

export default OrderSection;