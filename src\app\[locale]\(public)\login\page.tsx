"use client";
import type { NextPage } from "next";
import { Box, Typography } from "@mui/material";
import Link from "next/link";
import { useTranslations } from "next-intl";
import PublicPageContainer from "@/components/PublicPageContainer";
import SubmitButton from "@/components/buttons/SubmitButton";
import GoogleButton from "@/components/buttons/GoogleButton";
import TextField from "@/components/input/TextField";
import Image from "next/image";
import { ROUTES } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { LoginSchema } from "@/schemas/LoginSchema";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { GoogleLoginButton } from "./GoogleLoginButton";
import { useEffect } from "react";
import { showErrorToast, showToast } from "@/utils/toast";


type FormValue = z.infer<typeof LoginSchema>;

const Login: NextPage = () => {
  const t = useTranslations("login");
  const router = useRouter();
  
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    setError,
  } = useForm<FormValue>({
    defaultValues: {
      email: "",
      password: "",
    },
    resolver: zodResolver(LoginSchema),
  });

  const onSubmit: SubmitHandler<FormValue> = async (data) => {
    try {
      const res = await signIn("credentials", { ...data, redirect: false });
      if (res?.error) throw Error("invalidCredentials");
      router.push("/home");
    } catch (e) {
      setLoginError()
    }
  };

  const setLoginError = () => {
    setError("password", {
      type: "custom",
      message: "invalid_credential",
    });
    showErrorToast(401)
  }

  return (
    <PublicPageContainer>
      <Typography variant="h1" sx={{ marginBottom: 1 }}>
        {t("title")}
      </Typography>
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ marginBottom: 2.5 }}
      >
        {t("message")}
      </Typography>
      <GoogleLoginButton setLoginError={setLoginError} />
      <Typography
        variant="body1"
        sx={{ marginTop: 2.5, marginBottom: 2.5, textDecoration: "underline" }}
      >
        {t("login_by_email")}
      </Typography>
      <Box
        component={"form"}
        style={{ width: "100%" }}
        action="/api/auth/callback/credentials"
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              placeholder={t("placeholder_email")}
              error={errors?.email?.message}
            />
          )}
        />
        <Controller
          name="password"
          control={control}
          render={({ field }) => (
            <TextField
              value={field.value}
              onChange={field.onChange}
              size="medium"
              disabled={isSubmitting}
              type="password"
              placeholder={t("placeholder_password")}
              error={errors?.password?.message}
            />
          )}
        />
        <SubmitButton
          sx={{ marginTop: 1.25, marginBottom: 3.5 }}
          disabled={isSubmitting}
        >
          {t("button_login")}
        </SubmitButton>
      </Box>
      <Typography variant="body1" component={"span"} sx={{ marginBottom: 1.5 }}>
        {t.rich("new_to_funverse", {
          arrow: () => (
            <Box
              sx={{
                height: 20,
                width: 20,
                position: "relative",
                mx: 0.5,
                display: "inline-block",
                top: 4,
              }}
            >
              <Image src={"/images/arrow-right.svg"} layout="fill" alt="" />
            </Box>
          ),
          signup: (chunks) => (
            <Link href={ROUTES.SIGN_UP} className="link-black">
              {chunks}
            </Link>
          ),
        })}
      </Typography>
      <Link href={ROUTES.FORGOT_PASSWORD}>
        <Typography variant="body1" color="black">
          {t("button_forgot_password")}
        </Typography>
      </Link>
    </PublicPageContainer>
  );
};

export default Login;
