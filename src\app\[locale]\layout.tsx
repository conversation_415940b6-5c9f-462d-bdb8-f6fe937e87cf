
import "./globals.css";
import * as React from "react";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v14-appRouter";
import { ThemeProvider } from "@mui/material/styles";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import theme from "@/styles/theme";

import { DM_Sans } from "next/font/google";
import NextAuthProvider from "@/context/NextAuthProvider";
import { QueryProvider } from "@/context/QueryProvider";
import { DateAdapterProvider } from "@/context/DateAdapterProvider";
import { Metadata } from "next";
import OAuthLayer from './oAuthLayer';
import ToastProvider from "@/context/ToastProvider";
import ReduxProvider from "@/context/ReduxProvider";

export const metadata: Metadata = {
  // title: "Funverse | Metaverse E-Businuess",
  title:"INCUTix is your premium gateway to the biggest and most exclusive anime and film events organized by INCUBASE Studio and affiliates across Asia. We pride ourselves on offering VIP access to the biggest gatherings, where the vibrant worlds of animation and cinema come to life. As a fan, you're not just attending; you're part of an immersive experience that celebrates the stories you love."
};

const dmSans = DM_Sans({
  subsets: ["latin"],
  display: "swap",
});

export default async function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const messages = await getMessages();
  //console.log("local",messages)
  return (
    <html lang={locale} className={dmSans.className}>
      <body>
        <NextAuthProvider>
          <QueryProvider>
            <DateAdapterProvider>
              <AppRouterCacheProvider>
                <ThemeProvider theme={theme}>
                  <NextIntlClientProvider messages={messages}>
                    <OAuthLayer>
                      <ToastProvider>
                        <ReduxProvider>
                        {children}
                        </ReduxProvider>
                      </ToastProvider>
                    </OAuthLayer>
                  </NextIntlClientProvider>
                </ThemeProvider>
              </AppRouterCacheProvider>
            </DateAdapterProvider>
          </QueryProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
