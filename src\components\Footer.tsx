'use client'
import * as React from "react";
import { Box, Typography, Link, List, ListItem, colors } from "@mui/material";
import { COLORS } from "@/styles/colors";
import Stack from '@mui/material/Stack';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import FacebookOutlinedIcon from '@mui/icons-material/FacebookOutlined';
import InstagramIcon from '@mui/icons-material/Instagram';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";

export default function Footer() {
  const t = useTranslations("menu");
  const ft = useTranslations("footer");
  const pathname = usePathname();
  const textStyles = {
    textDecoration: "none",
    fontSize: { xs: '0.875rem', sm: '1rem' } 
  }

  const titleStyles = {
    fontSize:"32px",
    letterSpacing:"8px",
    background:"linear-gradient(90deg,#f48121 40%,#429b3f 0) 0 100%/100% 3px no-repeat",
    textDecoration: "none",
    minWidth:"50%"
  }

  const footerStyles = {
    textDecoration: "none",
    color:"black",
    ':hover': {
      color: "#ff7802"
  }
  }

  const navItems = [
    { 
      key: "home",
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US`
    },
    { 
      key: "about_incutix",
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/about` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/about`
    },
    { 
      key: "purchase_process", 
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/guide` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/guide`
    },
    { 
      key: "faq", 
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/faq` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/faq`
    }
  ];

  return (
    <>
    <Box
      sx={{
        backgroundColor: "#fff",
        padding: { xs: 2, md: 4 },
        borderTop:"1px solid hsla(0,0%,84%,.5)",
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' }, 
          gap: { xs: 4, md: 0 },
          paddingBottom: 4,
          flexWrap: "nowrap",
          margin:"0 auto",
          maxWidth:"1040px"
          // borderBottom: `1px solid ${COLORS.BLACK}`,
        }}
      >
        {/* <Stack spacing={2} sx={{ width: { xs: '100%', md: '25%' } }}>
        </Stack> */}

        <Stack spacing={2} sx={{ width: { xs: '100%', md: '50%' } }}>
          <Box>
          <h2><Link href="#" color="inherit" sx={titleStyles}>{ft('contact_us')}</Link></h2>
          </Box>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
            <MailOutlineIcon fontSize="small"/>
            <Link href="mailto:<EMAIL>" color="inherit" sx={{ ...textStyles, whiteSpace: 'nowrap' }}>
              <EMAIL>
            </Link>
          </Box>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
           <FacebookOutlinedIcon fontSize="medium"/>
            <Link href="https://www.facebook.com/incutix/" target="_blank" color="inherit" sx={textStyles}>
                INCUTix
            </Link>
          </Box>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
            <InstagramIcon fontSize="medium"/>
            <Link href="https://www.instagram.com/incutix/" target="_blank" color="inherit" sx={{ ...textStyles, whiteSpace: 'nowrap' }}>
                INCUTix
            </Link>
          </Box>
          <Box sx={{ display: "flex", alignItems: 'center', gap: 1 }}>
            <YouTubeIcon fontSize="medium"/>
            <Link href="https://www.youtube.com/@Incutix/videos" target="_blank" color="inherit" sx={textStyles}>
                INCUTix 
            </Link>
          </Box>
        </Stack>

        <Stack spacing={2} sx={{ width: { xs: '100%', md: '50%' },marginTop:"35px" }}>
          {/* <Link href="#" color="inherit" sx={textStyles}>{t("home")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("about_incutix")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("purchase_process")}</Link>
          <Link href="#" color="inherit" sx={textStyles}>{t("faq")}</Link> */}
                <List>
                  {navItems.map((item) => (
                    <ListItem 
                      key={item.key}
                      // sx={{ 
                      //   justifyContent: 'center',
                      //   py: 1
                      // }}
                    >
                      <Link
                        href={item.path}
                        sx={footerStyles}
                      >
                        {t(item.key)}
                      </Link>
                    </ListItem>
                  ))}
                </List>
        </Stack>


        {/* <Stack spacing={2} sx={{ 
          width: { xs: '100%', md: '25%' },
          mt: { xs: 2, md: 0 } 
        }}>
          <Link href="#" color="inherit" sx={textStyles}>{ft('follow_us')}</Link>
          <Box sx={{ 
            display: 'flex',
            gap: 2,
            justifyContent: { xs: 'flex-start', md: 'flex-start' }
          }}>
          </Box>
        </Stack> */}
      </Box>

      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 3, md: 0 },
          paddingTop: 4,
          flexWrap: "nowrap",
          margin:"0 auto",
          maxWidth:"1040px"
        }}
      >
        <Box sx={{ 
          width: { xs: '100%', md: '50%' },
          order: { xs: 2, md: 1 },
          paddingBottom:"50px"
        }}>
          <Box sx={{display:"flex"}}>
          <Typography variant="body2" sx={{ fontSize: { xs: '1rem', sm: '1rem' } }}>
          {ft('terms_of_service')} | 
          </Typography>&nbsp;
          <Typography variant="body2" sx={{ fontSize: { xs: '1rem', sm: '1rem' } }}>
           {ft('privacy_policy')}
          </Typography>
          </Box>
          {/* <Typography variant="body2" sx={{
            mt: 2,
            color: "rgba(119, 126, 144, 1)",
            fontSize: { xs: '0.75rem', sm: '0.875rem' }
          }}>
            © 2025 INCUTIX. All rights reserved.
          </Typography> */}
          <Typography variant="body2" sx={{ fontSize: { xs: '1rem', sm: '1rem' } }}>
          Copyright by INCUTix 2025
          </Typography><br></br>
          <Typography variant="body2" sx={{ 
            fontSize: { xs: '1rem', sm: '1rem' }
            }}>
            Powered by EasyLive Show Limited
          </Typography>
          <Typography variant="body2" sx={{
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            lineHeight: 1.4
          }}>
            Address: B6, 25/F., TML Tower,<br></br>
            3 Hoi Shing Road, Tsuen Wan, Hong Kong
          </Typography>
        </Box>

      </Box>
    </Box>
    </>
  );
}