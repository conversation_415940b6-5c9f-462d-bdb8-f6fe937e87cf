'use client'
import * as React from "react";
import { 
  <PERSON>, 
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer,
  List,
  ListItem,
  useMediaQuery,
  Theme,
  CircularProgress,
  Divider,
  But<PERSON>,
  Typography,
  Badge,
} from "@mui/material";
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import Image from "next/image";
import { COLORS } from "@/styles/colors";
import AccountMenu from "./buttons/AccountMenu";
import { useLocale, useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import  LoginAndRegisterSection from "@/components/LoginAndRegisterSection";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import { useSession } from "next-auth/react";
import ShoppingBasketOutlinedIcon from '@mui/icons-material/ShoppingBasketOutlined';
import ShoppingCart from '@/components/shopping-cart/ShoppingCart';
import {apiClient } from "@/utils/apiClient";
import {CartCount} from "@/interface/ICartCount";
import { useDispatch,useSelector } from "react-redux";
import { fetchCartCount } from "@/redux/cartCountSlice";
import type { AppDispatch, RootState } from "@/redux/store";
import{ fetchUserData } from "@/redux/userSlice";
import PersonIcon from '@mui/icons-material/Person';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

export default function Header() {
  const t = useTranslations("menu");
  // const [selectedLink, setSelectedLink] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { data: session , status } = useSession();
  // const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));
  const pathname = usePathname();
  const router = useRouter();
  const isLoggedIn = useMemo(() => status === "authenticated", [status]);
  const [selectedLink, setSelectedLink] = useState(() => {
    if (pathname === '/zh/all-products' || pathname === '/all-products') {
      return 'all_products';
    }
    // Add similar checks for other routes if needed
    return null;
  });
  const [cartOpen, setCartOpen] = useState<boolean>(false);
  const [cartCount,setCartCount] = useState<CartCount | undefined>(undefined);
  const dispatch = useDispatch<AppDispatch>(); // 使用泛型讓 dispatch 支援 thunk
  const cartCounts = useSelector((state: RootState) => state.cartCount);
  const {userData, loading, error} = useSelector((state: RootState)=> state.userData);
  const currentLocale = useLocale();

  const handleCartOpen = () => setCartOpen(true);
  const handleCartClose = () => setCartOpen(false);

  const tokenMatch = pathname.match(/^(\/zh)?(\/all-products\/[^/]+)/);
  const extractedPath = tokenMatch ? tokenMatch[2] : null;
  useEffect(()=>{
      dispatch(fetchCartCount())
      dispatch(fetchUserData())
      setIsLoading(true);
  },[])

  const localeToLanguageCodes: Record<string, string> = {
        en: 'en-US',
        zh: 'zh-HK'
  };

  const languageCodes = localeToLanguageCodes[currentLocale] || 'en-US';

  const handleClickProfileLink = () =>{
    router.push(`${process.env.NEXT_PUBLIC_INCUTIX_URL}/${languageCodes}/user/profile`);
  }

  console.log("userData Redux",userData)
  console.log("userData Redux loading",loading)
  console.log("userData Redux error",error)

  const profileIcon = () =>{
    return(
      <>
      <Button 
        sx={{
          backgroundColor: "#ff7802",
          borderRadius: "12px",
          '&:hover': {
            backgroundColor: "#ff7802", 
            boxShadow: "0 0 15px #ff7802" 
          }
        }}
        onClick={handleClickProfileLink}
      >
        <PersonIcon sx={{ color: "#fff" }} />
        <ArrowDropDownIcon sx={{ color: "#fff" }} />
      </Button>
      </>
    )
  }
    
  const renderSignInStatus = () => {
      // Handle loading state
      if (!isLoading) {
          return <CircularProgress sx={{color:"#ff7802"}} size={24}/>;
      }
      
      if (!userData) {
          return (
            <>
            <LoginAndRegisterSection />
            </>
          );
      }
      
      if (userData && !isMobile) {
          return (
              <>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <ShoppingBasketOutlinedIcon onClick={handleCartOpen} sx={{ cursor: "pointer" }} />
                  <Typography sx={{marginRight:"10px"}}>{cartCounts.data?.count}</Typography>
                   {profileIcon()}
                </Box>
              </>
          );
      }

      // Fallback (optional)
      return null;
  };
  

  const navItems = [
    { 
      key: "about_incutix",
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/about` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/about`
    },
    // { key: "all_products", path:  tokenMatch ? `${extractedPath}`:"/all-products" },
    { 
      key: "all_products",
      path: pathname.startsWith("/zh/") ? `/zh/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}` : `/all-products/${process.env.NEXT_PUBLIC_EMPTY_TOKEN}`
    },
    // { 
    //   key: "featured_events", 
    //   path: pathname.startsWith("/zh/") ? "https://incutix.com/zh-HK" : "https://incutix.com/en-US"
    // },
    { 
      key: "purchase_process", 
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/guide` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/guide`
    },
    { 
      key: "faq", 
      path: pathname.startsWith("/zh/") ? `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK/faq` : `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US/faq`
    }
  ];
  
  const textStyles = (isSelected: boolean) => ({
    color: isSelected ? '#ff7802' : 'black',
    textDecoration: 'none',
    fontSize: isMobile ? '.8rem' : '.8rem',
    position: 'relative',
    // '&:hover': {
    //   color: 'rgba(79, 183, 71, 1)', 
    // },
    // '&:focus': {
    //   color: 'rgba(79, 183, 71, 1)', 
    // },
    fontWeight: 700,
    letterSpacing: '2px',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: '-5px',
      left: isSelected ? '0' : '50%',
      width: isSelected ? '100%' : '0',
      height: '2px',
      backgroundColor: '#ff7802',
      transition: 'all 0.3s ease',
      transform: isSelected ? 'translateX(0)' : 'translateX(-50%)'
    },
    '&:hover::after': {
      left: '0',
      width: '100%',
      transform: 'translateX(0)'
    }
  });

  const handleLinkClick = async (link: any) => {
    setIsLoading(true);
    setSelectedLink(link);
    if (isMobile) setMobileOpen(false);
  
    await new Promise((resolve) => setTimeout(resolve, 2000)); 
  
    setIsLoading(false);
    const item = navItems.find(item => item.key === link);
    if (item) {
      router.push(item.path); 
    }
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleBackHome = () => {
    router.push('/all-products');
    if(isMobile) setMobileOpen(false);
  };

  useEffect(() => {
    if (pathname === '/zh/all-products' || pathname === '/all-products') {
      setSelectedLink('all_products');
    } else {
      // Handle other route highlights if needed
      const currentItem = navItems.find(item => 
        item.path === pathname || 
        (pathname.startsWith('/zh/') && item.path === `${process.env.NEXT_PUBLIC_INCUTIX_URL}/zh-HK${pathname.substring(3)}`) ||
        (!pathname.startsWith('/zh/') && item.path === `${process.env.NEXT_PUBLIC_INCUTIX_URL}/en-US${pathname}`)
      );
      setSelectedLink(currentItem?.key || null);
    }
  }, [pathname]);

  const drawerContent = (
    <Box
      sx={{
        // width: 250,
        padding: 2,
        // height: '100%',
        backgroundColor: COLORS.WHITE,
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column' 
      }}
    >
      <Box sx={{ 
        display: 'flex',
        justifyContent: 'space-between',
        mb: 3,
      }}>
        <Box>
        <Image
            src="/images/IncutixLogo.png"
            height={30}
            width={152}
            alt="incutix-logo"
            priority
          />
          &nbsp;
          &nbsp;
          <Image
            src="/images/easylive_claim_Logo-01.png"
            height={30}
            width={152}
            alt="incutix-logo"
            priority
          />
          </Box>
        <IconButton onClick={handleDrawerToggle}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Divider></Divider>
      <Box sx={{
      flexGrow: 1, 
      display: 'flex',
      flexDirection: 'column'
      }}>
      <List>
        {navItems.map((item) => (
          <ListItem 
            key={item.key}
            // sx={{ 
            //   justifyContent: 'center',
            //   py: 1
            // }}
          >
            <Link
              href={item.path}
              sx={textStyles(selectedLink === item.key)}
              onClick={() => handleLinkClick(item.key)}
            >
              {t(item.key)}
            </Link>
          </ListItem>
        ))}
      </List>
        {renderSignInStatus()}
      </Box>
    </Box>
  );

  return (
    <Box
      component="header"
      sx={{
        background: "#fff",
        height: { xs: 70, md: 80 },
        paddingX: { xs: 2, md: '27px' },
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        height="100%"
      >
        {/* 左侧Logo */}
        <Box
          // onClick={handleBackHome}
          sx={{
            flexShrink: 0,
            '& img': {
              width: { xs: 120, md: 150, lg:200 },
              height: 'auto'
            }
          }}
        >
          <Image
            src="/images/IncutixLogo.png"
            height={30}
            width={152}
            alt="incutix-logo"
            priority
          />
          &nbsp;
          &nbsp;
          <Image
            src="/images/easylive_claim_Logo-01.png"
            height={30}
            width={152}
            alt="incutix-logo"
            priority
          />
        </Box>

      {/* 移动端菜单按钮 */}
        {isMobile && (
          <Box 
          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
          >
            {
              userData && 
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <ShoppingBasketOutlinedIcon onClick={handleCartOpen} sx={{ cursor: "pointer" }} />
                <Typography>{cartCounts.data?.count}</Typography>
              </Box>
            }
            <IconButton
              color="inherit"
              edge="end"
              onClick={handleDrawerToggle}
              sx={{ ml: 1 }}
            >
              <MenuIcon fontSize="medium" />
            </IconButton>
          </Box>
        )}
            {/* 桌面导航 */}
            {!isMobile && (
          <Box
            sx={{
              display: 'flex',
              alignItems: "center",
              gap: { 
                md: 3, 
                // lg: 4 
              },
              flexGrow: 1,
              justifyContent: 'flex-end',
              maxWidth: 800
            }}
          >
              <>
                {navItems.map((item) => (
                  <Link 
                    key={item.key}
                    href={item.path}
                    sx={textStyles(selectedLink === item.key)}
                    onClick={() => handleLinkClick(item.key)}
                  >
                    {t(item.key)}
                  </Link>
                ))}
                {/* {status === "authenticated" ? <AccountMenu /> : <LoginAndRegisterSection/>} */}
                {/* {renderProfileLogo()} */}
                {/* <LoginAndRegisterSection /> */}
                {renderSignInStatus()}
                <LanguageSwitcher/>
              </>
          </Box>
        )}

        {/* 移动端抽屉菜单 */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true
          }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: "100%",
              backgroundColor:"#fff"
            }
          }}
        >
          <Box>
          {drawerContent}
          {/* {renderProfileLogo()} */}
          {/* <LoginAndRegisterSection />
          <LanguageSwitcher/> */}
          </Box>
          <Box sx={{ 
            marginTop: 'auto',        
            paddingBottom: "30px",
            paddingLeft:"20px"
            }}>
            <LanguageSwitcher/>
          </Box>
        </Drawer>
         <ShoppingCart open={cartOpen} width={isMobile ? '100%' : 350}  onClose={handleCartClose}/>
      </Box>
    </Box>
  );
}