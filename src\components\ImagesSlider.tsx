"use client"
import { Box, IconButton } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';

interface ImagesSliderProps {
  images: string[]; 
  interval?: number; 
  height?: number | string; 
  blurIntensity?: number;
}

const ImagesSlider = ({
  images,
  interval = 3000,
  height = 600,
  blurIntensity = 150,
}: ImagesSliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 自動輪播邏輯
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [images.length, interval]);

  const goToPrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  return (
    <Box sx={{
      paddingRight:2,
      paddingLeft:2,
      mb:2
    }}>
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          margin: '0 auto',
          overflow: 'hidden',
          height: height, // 使用外部傳入的高度
          borderRadius:"32px"
        }}
      >
        {/* 背景圖片 */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: `url(${images[currentIndex]})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: `blur(${blurIntensity}px)`, // 使用外部傳入的模糊強度
            zIndex: 1,
          }}
        />

        {/* 前景圖片 */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <img
            src={images[currentIndex]}
            alt={`Slide ${currentIndex}`}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              borderRadius: '8px',
            }}
          />
        </Box>

        {/* 左側按鈕 */}
        <IconButton
          onClick={goToPrev}
          sx={{
            position: 'absolute',
            top: '50%',
            left: '10px',
            transform: 'translateY(-50%)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            color: 'white',
            zIndex: 3,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
            },
          }}
        >
          <ChevronLeft />
        </IconButton>

        {/* 右側按鈕 */}
        <IconButton
          onClick={goToNext}
          sx={{
            position: 'absolute',
            top: '50%',
            right: '10px',
            transform: 'translateY(-50%)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            color: 'white',
            zIndex: 3,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
            },
          }}
        >
          <ChevronRight />
        </IconButton>
      </Box>
    </Box>
  );
};

export default ImagesSlider;