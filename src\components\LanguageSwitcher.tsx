"use client";
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Drawer,
  useMediaQuery,
  Theme,
  ToggleButton,
  ToggleButtonGroup,
  Box,
} from "@mui/material";

const LanguageSwitcher = () => {
  const router = useRouter();
  const [currentLocale, setCurrentLocale] = useState('en');
  const [loading, setLoading] = useState(false);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));

  useEffect(() => {
    // const locale = window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
    // setCurrentLocale(locale);
    const path = window.location.pathname;
    let locale = 'en'; 
    if (path.startsWith('/zh')) locale = 'zh';
    else if (path.startsWith('/th')) locale = 'th';
    setCurrentLocale(locale);
  }, []);

  //   const handleLocaleChange = (event: React.MouseEvent<HTMLElement>, newLocale: string | null) => {
  //   if (newLocale !== null && newLocale !== currentLocale) {
  //     setLoading(true);
  //     const pathWithoutLocale = window.location.pathname.replace(`/${currentLocale}`, '');
  //     window.location.href = `/${newLocale}${pathWithoutLocale}`;
  //   }
  // };

  // const handleSelectChange = (event: SelectChangeEvent) => {
  //   const newLocale = event.target.value as string;
  //   setLoading(true);
  //   const pathWithoutLocale = window.location.pathname.replace(`/${currentLocale}`, '');
  //   window.location.href = `/${newLocale}${pathWithoutLocale}`;
  // };

const handleLocaleChange = (event: React.MouseEvent<HTMLElement>, newLocale: string | null) => {
  if (newLocale !== null && newLocale !== currentLocale) {
    setLoading(true);
    const url = new URL(window.location.href);
    
    if (url.searchParams.has('locales')) {
      // Case 1: Update locales parameter if it exists
      url.searchParams.set('locales', newLocale);
      window.location.href = url.toString();
    } else {
      // Case 2: Default path-based locale switching
      const pathWithoutLocale = window.location.pathname.replace(`/${currentLocale}`, '');
      window.location.href = `/${newLocale}${pathWithoutLocale}${window.location.search}`;
    }
  }
};

const handleSelectChange = (event: SelectChangeEvent) => {
  const newLocale = event.target.value as string;
  setLoading(true);
  const url = new URL(window.location.href);
  
  if (url.searchParams.has('locales')) {
    // Case 1: Update locales parameter if it exists
    url.searchParams.set('locales', newLocale);
    window.location.href = url.toString();
  } else {
    // Case 2: Default path-based locale switching
    const pathWithoutLocale = window.location.pathname.replace(`/${currentLocale}`, '');
    window.location.href = `/${newLocale}${pathWithoutLocale}${window.location.search}`;
  }
};

  return (
    <Box>
      {isMobile ? (
        <ToggleButtonGroup
          value={currentLocale}
          exclusive
          onChange={handleLocaleChange}
          aria-label="language selection"
          size="small"
          sx={{
            '& .MuiToggleButton-root': {
              border: 'none',
              '&.Mui-selected, &.Mui-selected:hover': {
                backgroundColor: 'transparent',
                color: 'inherit',
              }
            }
          }}
        >
          <ToggleButton value="zh" aria-label="chinese" sx={{
            color:"black",
            fontSize:"1.25rem",
            fontWeight:"700",
            }}>
            繁
          </ToggleButton>
          <ToggleButton value="en" aria-label="english" sx={{
            color:"black",
            fontSize:"1.25rem",
            fontWeight:"700",
            }}>
            EN
          </ToggleButton>
          <ToggleButton value="th" aria-label="thai" sx={{
            color:"black",
            fontSize:"1.25rem",
            fontWeight:"700",
            }}>
            ไทย
          </ToggleButton>
        </ToggleButtonGroup>
      ) : (
        <FormControl size="small">
          <Select
            labelId="language-select-label"
            id="language-select"
            value={currentLocale}
            label="Language"
            onChange={handleSelectChange}
            disabled={loading}
    sx={{
      "& .MuiOutlinedInput-notchedOutline": {
        border: "none",
      },
      "&:hover .MuiOutlinedInput-notchedOutline": {
        border: "none",
      },
      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
        border: "none",
      },
    }}
    MenuProps={{
      PaperProps: {
        sx: {
          bgcolor: "#fff8f2",
          "& .MuiMenuItem-root": {
            color: "black", 
            "&:hover": {
              bgcolor: "#ffead9", 
            },
            "&.Mui-selected": {
              bgcolor: "#ffd4b3",
              color: "black", 
              "&:hover": {
                bgcolor: "#ffc799", 
              },
            },
            "&.Mui-focusVisible": {
              bgcolor: "#ffead9",
            },
          },
        },
      },
    }}
          >
            <MenuItem value="en">EN</MenuItem>
            <MenuItem value="zh">繁</MenuItem>
            <MenuItem value="th">ไทย</MenuItem>
          </Select>
        </FormControl>
      )}
    </Box>
  );
};

export default LanguageSwitcher;