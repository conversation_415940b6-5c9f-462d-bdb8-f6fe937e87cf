"use client"
import CustomButton from "@/components/buttons/CustomButton";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';
import { IconButton, Link, Theme, useMediaQuery } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CustomTextField from "@/components/input/CustomTextField"
import { signIn } from 'next-auth/react';
import { ROUTES } from "@/utils/constants";
import {showErrorPopUp,showSuccessPopUp} from "@/utils/toast";
import LoginModal from "@/components/modals/LoginModal";
import ForgetPasswordModal from "@/components/modals/ForgetPasswordModal";
import { boolean } from "zod";
import xior from "xior";
import RegisterModal from "./modals/RegisterModal";

const LoginAndRegisterSection = () => {
    const t = useTranslations("navbar");

    const style = {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 400,
        bgcolor: 'background.paper',
        boxShadow: 24,
        p: 4,
    };

    const [email, setEmail] = useState<string>('');
    const [forgotPasswordEmail,setForgotPasswordEmail] = useState<string>('');
    const [sentEmail,setSentEmail] = useState<boolean>(false);
    const [password, setPassword] = useState<string>('');
    const [openLogin, setOpenLogin] = useState<boolean>(false);
    const [openRegister, setOpenRegister] = useState<boolean>(false);
    const [openForgetPassoword, setForgetPassoword] = useState<boolean>(false);
    const [isSubmitting,setIsSubmitting] = useState<boolean>(false);
    const { showToast: showSuccess } = showSuccessPopUp();
    const { showToast: showError } = showErrorPopUp();
    const [rememberMe, setRememberMe] = useState<boolean>(true);
    const [registeredEmail,setRegisteredEmail] = useState<string>("");
    const [countdown, setCountdown] = useState<number>(0);
    const [isValidatedEmail,setIsValidatedEmail] = useState<boolean>(false);
    const [isVerifiedEmail,setIsVerifiedEmail] = useState<boolean>(false);
    const [otp, setOtp] = useState<string>("");
    const [userName,setUserName] = useState<string>("");
    const [newPassword, setNewPassword] = useState<string>("");
    const [confirmNewPassword, setConfirmNewPassword] = useState<string>("");
    const [isCheckTermsOfService, setIsCheckTermsOfService] = useState<boolean>(false);
    const [isCheckReceivePromotions, setIsCheckReceivePromotions] = useState<boolean>(false);
    const [currentLocale, setCurrentLocale] = useState('en');
    const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down('md'));

    const handleOpenLogin = () => setOpenLogin(true);
    const handleCloseLogin = () => setOpenLogin(false);

    useEffect(() => {
        const locale = window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
        setCurrentLocale(locale);
    }, []);

    useEffect(() => {
        const savedEmail = localStorage.getItem('email');
        const savedPassword = localStorage.getItem('password');
        if (savedEmail) setEmail(savedEmail);
        if (savedPassword) setPassword(savedPassword);
      }, []);

    const localeToLanguageCode: Record<string, string> = {
        en: 'en-US',
        zh: 'zh-HK'
    };

    const languageCode = localeToLanguageCode[currentLocale] || 'en-US';

    const handleLoginRedirectUrl = () =>{
        window.location.href = `${process.env.NEXT_PUBLIC_INCUTIX_URL}/${languageCode}/login`;
        // const userIdToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
        // localStorage.setItem("userIdToken", userIdToken);
    }

    const handleRegisterRedirectUrl = () =>{
        window.location.href = `${process.env.NEXT_PUBLIC_INCUTIX_URL}/${languageCode}/signUp`;
    }
    
    const handleCloseForgetPassword = () => setForgetPassoword(false);


    const handleOtpChange = (value: string) => {
        setOtp(value);
    }

    const handleTermsOfServiceChange = (event:any) =>{
        setIsCheckTermsOfService(event.target.checked);
    }

    const handleReceivePromotionsChange = (event:any) =>{
        setIsCheckReceivePromotions(event.target.checked)
    }

    const handleUserNameChange = (event:any) =>{
        setUserName(event.target.value);
    }

    const handleNewPassword = (newPassword:string) =>{
        setNewPassword(newPassword);
    }

    const handleConfirmNewPassword = (event:any) =>{
        setConfirmNewPassword(event.target.value);
    }

    const handleOpenForgetPassword = () => {
        setOpenLogin(false); 
        setOpenRegister(false);
        setForgetPassoword(true);
    }

    const handleForgotPasswordEmail = (event:any) =>{
        setForgotPasswordEmail(event.target.value)
    }

    const handleRegisteredEmailChange = (event:any) =>{
        setRegisteredEmail(event.target.value)
    }

    const handleOpenRegister = () => {
        setOpenLogin(false); 
        setOpenRegister(true); 
    };

    const handleRememberMeChange = (event:any) =>{
        setRememberMe(event.target.checked)
    }

    const handleOpenLoginInRegisterModel = () =>{
        setOpenLogin(true); 
        setOpenRegister(false); 
    }

    const handleCloseRegister = () => setOpenRegister(false);  

    const handleEmailChange = (event:any) =>{
        setEmail(event.target.value)
      }
    
    const handlePasswordChange = (event:any) => {
        setPassword(event.target.value)
      }
    
    const handleSubmit = async (e:any) => {
        e.preventDefault();
    
        setIsSubmitting(true)
        const result = await signIn('credentials', {
          redirect: false,
          email,
          password,
          type: 'credentials', 
        });
        setIsSubmitting(false)
        // if(result){
        //     window.location.href = (ROUTES.HOME)
        // }
        if(result?.error){
            showError("Login failed, please try again");
        }else{
            showSuccess("Login successfully");
            if (rememberMe) {
                localStorage.setItem('email', email);
                localStorage.setItem('password', password);
              } else {
                localStorage.removeItem('email');
                localStorage.removeItem('password');
              }
        
            window.location.href = (ROUTES.HOME)
        }
      };

      const getCurrentLocale = () => {
        return window.location.pathname.startsWith('/zh') ? 'zh' : 'en';
      };

      const handleForgotPasswordApi = async () => {
        const locale = getCurrentLocale();

        try {
            const result = await xior.post('/api/auth/forgot-password', {
                email: forgotPasswordEmail ,
                language:locale
            });
    
            // console.log("ForgotPassword successful", result.data);
            showSuccess("Password reset email sent successfully!"); 
            setSentEmail(true);
        } catch (error) {
            console.error("ForgotPassword failed", error);
            showError ("Failed to send password reset email. Please try again."); 
        }
    };

    const handleRefreshForgotPasswordApi = async () =>{
        const locale = getCurrentLocale();

        try{
            const response = await xior.post('/api/auth/forgot-password', {
                email: forgotPasswordEmail ,
                language:locale
            });

            console.log("ForgotPassword Refresh successful", response.data);
            showSuccess("Password reset email sent successfully!");

            if (countdown === 0) {
                // Start the countdown
                setCountdown(30);
            }

        }catch(err){
            console.error("ForgotPassword failed", err);
            showError ("Failed to send password reset email. Please try again."); 
        }
    }
    
    const handleResnetOtpApi = async () =>{
        
        try{

            const response = await xior.post('/api/auth/resend-otp',{
                email:registeredEmail
            })

                console.log("OTP resent successfully", response.data);
                showSuccess("OTP resent successfully, please check your email");
                if (countdown === 0) {
                    // Start the countdown
                    setCountdown(30);
                }
        }catch(err){
            console.error("resend otp failed", err);
            showError ("Failed to reend otp. Please try again."); 
        }
    }

    const handleRegisterAccountApi = async () =>{
        try{

            const response = await xior.post('/api/auth/register',{
                email:registeredEmail,
                role: "visitor"
            })

            console.log("OTP sent successfully", response.data);
            showSuccess("OTP sent successfully, please check your email");
            setIsValidatedEmail(true)
        }catch(err){
            console.error("register account failed", err);
            showError ("Failed to register account. Please try again."); 
        }
    }

    const handleVerifyOtpApi = async () =>{
        try{

            const response = await xior.post('/api/auth/verify-otp',{
                email:registeredEmail,
                otp:otp
            })

            console.log("The OTP was verified successfully.", response.data);
            showSuccess("The OTP was verified successfully.");
            setIsVerifiedEmail(true)
        }catch(err){
            console.error("The OTP verification failed.", err);
            showError ("The OTP verification failed, Please try again."); 
        }
    }

    // console.log("otp code",otp )  
    // console.log("registeredEmail",registeredEmail ) 
    // console.log("user name", userName)
    // console.log("password", newPassword)  
    // console.log("confirm new password", confirmNewPassword)
    // console.log("Check terms of service", isCheckTermsOfService)
    // console.log("Check receive promotions", isCheckReceivePromotions)

    const handleLoginAfterSetupProfileApi = async (e:any) => {
        e.preventDefault();
    
        setIsSubmitting(true)
        const result = await signIn('credentials', {
          redirect: false,
          email:registeredEmail,
          password:newPassword,
          type: 'credentials', 
        });
        setIsSubmitting(false)
        // if(result){
        //     window.location.href = (ROUTES.HOME)
        // }
        if(result?.error){
            showError("Login failed, please try again");
        }else{
            showSuccess("Login successfully");
            if (rememberMe) {
                localStorage.setItem('email', email);
                localStorage.setItem('password', password);
              } else {
                localStorage.removeItem('email');
                localStorage.removeItem('password');
              }
        
            window.location.href = (ROUTES.HOME)
        }
      };



    const handleSetupProfileApi = async () => {
        
        if(!userName){
            showError("User name is required.");
            return;
        }

        if(!newPassword){
            showError("New password is required.");
            return;
        }

        if(!confirmNewPassword){
            showError("Confirm new password is required.");
            return;
        }

        if(newPassword !== confirmNewPassword){
            showError("Passwords do not match.");
            return;
        }
        try{
            
            const response = await xior.post('/auth/verify',{
                email:registeredEmail,
                otp:parseInt(otp),
                name:userName,
                password:newPassword,
                receivePromotions:isCheckReceivePromotions
            },
            {
                baseURL: process.env.NEXT_PUBLIC_API_BASE,
              }
            )

            console.log("setup profile successfully", response.data)
            showSuccess("Setup profile successfully");

            await handleLoginAfterSetupProfileApi({ preventDefault: () => {} });


        }catch(err){
            console.log("setup profile failed ",err);
            showError ("failed tp setup profile, Please try again."); 
        }
    }

    const renderAuthButtons = () => {
        const buttons = [
          <CustomButton 
            key="login"
            namespace="navbar" 
            label="incutix-login"
            color="inherit"
            hoverColor="#ffffff"
            backgroundColor="#FFFFFF"
            hoverBackgroundColor="#ff7802" 
            borderRadius="20px"
            border="2px solid #ff7802"
            onClick={handleLoginRedirectUrl}
          />,
          <CustomButton 
            key="register"
            namespace="navbar" 
            label="incutix-register"
            color="primary"
            backgroundColor="#ff7802"
            hoverBackgroundColor="#ff7802" 
            borderRadius="20px"
            border="2px solid #ff7802"
            boxShadow="0 0 5px #ccc" 
            hoverBoxShadow="0 0 10px #ff7802"
            onClick={handleRegisterRedirectUrl}
          />
        ];
      
        if (isMobile) {
          return (
            <div style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                gap: '10px',
                width:"100px",
                paddingLeft:"10px"
                }}>
              {buttons}
            </div>
          );
        } else {
          return (
            <div style={{ display: 'flex', gap: '10px' }}>
              {buttons}
            </div>
          );
        }
      };
      

    useEffect(() => {
        let timer: NodeJS.Timeout;
        if (countdown > 0) {
            timer = setInterval(() => {
                setCountdown(prev => prev - 1);
            }, 1000);
        }
        return () => clearInterval(timer); 
    }, [countdown]);    
    
    return (
        <>
            {/* <CustomButton  namespace="navbar" label="login" 
            onClick={handleOpenLogin} 
            /> */}
            {renderAuthButtons()}
                
            {/* Login Modal */}
            <LoginModal
            openLogin={openLogin}
            handleCloseLogin={handleCloseLogin}
            handleOpenRegister={handleOpenRegister}
            email={email}
            handleEmailChange={handleEmailChange}
            password={password}
            handlePasswordChange={handlePasswordChange}
            isSubmitting={isSubmitting}
            handleSubmit={handleSubmit}
            rememberMe={rememberMe}
            handleRememberMeChange={handleRememberMeChange}
            handleOpenForgetPassword={handleOpenForgetPassword}
            namespace="navbar"
            />

            {/* Register Modal */}
            <RegisterModal 
            openRegister={openRegister}
            handleCloseRegister={handleCloseRegister}
            handleOpenLoginInRegisterModel={handleOpenLoginInRegisterModel}
            registeredEmail={registeredEmail}
            handleRegisteredEmailChange={handleRegisteredEmailChange}
            handleRegisterAccountApi={handleRegisterAccountApi}
            isValidatedEmail={isValidatedEmail}
            isVerifiedEmail={isVerifiedEmail}
            otp={otp}
            handleOtpChange={handleOtpChange}
            countdown={countdown}
            handleResnetOtpApi={handleResnetOtpApi}
            handleVerifyOtpApi={handleVerifyOtpApi}
            userName={userName}
            newPassword={newPassword}
            confirmNewPassword={confirmNewPassword}
            handleUserNameChange={handleUserNameChange}
            handleNewPassword={handleNewPassword}
            handleConfirmNewPassword={handleConfirmNewPassword}
            isCheckTermsOfService={isCheckTermsOfService}
            isCheckReceivePromotions={isCheckReceivePromotions}
            handleTermsOfServiceChange={handleTermsOfServiceChange}
            handleReceivePromotionsChange={handleReceivePromotionsChange}
            handleSetupProfileApi={handleSetupProfileApi}
            />
            
            {/* Forget Password Modal */}
            <ForgetPasswordModal
            openForgetPassoword={openForgetPassoword}
            handleCloseForgetPassword={handleCloseForgetPassword}
            forgotPasswordEmail={forgotPasswordEmail}
            handleForgotPasswordEmail={handleForgotPasswordEmail}
            handleForgotPasswordApi={handleForgotPasswordApi}
            handleRefreshForgotPasswordApi={handleRefreshForgotPasswordApi}
            sentEmail={sentEmail}
            countdown={countdown}
            />
        </>
    );
};

export default LoginAndRegisterSection;