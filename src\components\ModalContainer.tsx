import { Box, BoxProps } from "@mui/material";
import * as React from "react";

const ModalContainer = (props: { children: React.ReactNode } & BoxProps) => {
  const { sx, children, ...otherProps } = props;
  return (
    <Box
      sx={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        background: "#fff",
        border: "2px solid #000000",
        borderRadius: "10px",
        padding: "28px 28px 32px 28px",
        minWidth: 380,
        display: "flex",
        flexDirection: "column",
        ...sx,
      }}
      {...otherProps}
    >
      {children}
    </Box>
  );
};

export default ModalContainer;
