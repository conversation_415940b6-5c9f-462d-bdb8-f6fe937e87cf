import React from 'react';
import Snackbar from '@mui/material/Snackbar';

type Props ={
    open:boolean;
    onClose?:()=> void;
    message:string;
    autoHideDuration?:number;
    vertical?:string;
    horizontal?:string;
    color?:string;
    backgroundColor?:string;
}

const CustomSnackbar = ({
  open,
  onClose,
  message,
  autoHideDuration,
  color,
  backgroundColor
}:Props) => {
  return (
    <Snackbar
      anchorOrigin={{ vertical:'bottom', horizontal:'center' }}
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={onClose}
      message={message}
      sx={{
        position: 'fixed',
        left: '50%',
        transform: 'translateX(-50%)',
        bottom: '20px',
        zIndex: 9999,
        '& .MuiSnackbarContent-root': {
          color: color,
          backgroundColor: backgroundColor,
          boxShadow:"0px 12px 32px 0px rgba(0, 0, 0, 0.1)",
          ustifyContent: 'center', 
          textAlign: 'center',           '& .MuiSnackbarContent-message': {
            width: '100%',          // 讓訊息佔滿整個空間
            display: 'flex',
            justifyContent: 'center' // 確保內容居中
          }
        }
      }}
    />
  );
};

export default CustomSnackbar;