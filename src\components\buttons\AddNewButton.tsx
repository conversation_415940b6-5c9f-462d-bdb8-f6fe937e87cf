import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import AddRoundedIcon from "@mui/icons-material/AddRounded";
import { ButtonHeadTable } from "./styled";

const AddNewButton = (props: ButtonProps) => {
  const t = useTranslations("common");

  return (
    <ButtonHeadTable variant="contained" endIcon={<AddRoundedIcon />} sx={{marginRight:0, minWidth:'100px'}} {...props}> 
      {t("button_add_new")}
    </ButtonHeadTable>
  );
};

export default AddNewButton;
