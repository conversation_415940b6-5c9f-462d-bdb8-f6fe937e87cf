import React from "react";
import { useTranslations } from "next-intl";
import { BaseButton } from "./styled";
import { COLORS } from "@/styles/colors";

type Props = {
  disabled?: boolean;
  label?: string;
  onAction: () => void;
  style?: React.CSSProperties;
};

const CancelButton = (props: Props) => {
  const { disabled, label, onAction, style } = props;

  const t = useTranslations();
  return (
    <BaseButton
      disabled={disabled}
      variant="contained"
      color="secondary"
      type="button"
      onClick={onAction}
      sx={{ marginX: "0.5rem", color: COLORS.BLACK, ...style }}
    >
      {label || t("common.button_cancel")}
    </BaseButton>
  );
};

export default CancelButton;
