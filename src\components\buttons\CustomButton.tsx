import React from 'react';
import { Button, styled, ButtonProps } from '@mui/material';
import { useTranslations } from 'next-intl';

interface StyleProps {
  backgroundColor?: string;
  hoverBackgroundColor?: string;
  color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  hoverColor?: string;
  borderRadius?: string;
  border?: string;
  padding?: string;
  boxShadow?: string;
  hoverBoxShadow?: string;
}

interface MyButtonProps extends StyleProps, Omit<ButtonProps, 'color'> {
  label: string;
  namespace?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  onClick?: (event?: any) => void;
}

const CustomButton = styled(Button, {
  shouldForwardProp: (prop) => ![
    'backgroundColor',
    'hoverBackgroundColor',
    'borderRadius',
    'boxShadow',
    'hoverBoxShadow',
    'hoverColor'
  ].includes(prop as string),
})<StyleProps>(({ 
  backgroundColor = 'rgba(79, 183, 71, 1)', 
  hoverBackgroundColor = 'rgba(79, 183, 71, 0.8)',
  boxShadow,
  hoverBoxShadow = '0 0 10px #ff7802',
  color = '#fff',
  hoverColor,
  borderRadius = '4px',
  border = '',
  padding = '6px 16px'
}) => ({
  backgroundColor,
  color,
  borderRadius,
  border,
  padding,
  boxShadow,
  '&:hover': {
    backgroundColor: hoverBackgroundColor,
    boxShadow: hoverBoxShadow,
    color: hoverColor || color
  },
}));

const MyButton: React.FC<MyButtonProps> = ({ 
  label,
  disabled,
  namespace,
  icon,
  onClick,
  backgroundColor,
  hoverBackgroundColor,
  color,
  borderRadius,
  border,
  padding,
  boxShadow,
  hoverBoxShadow,
  ...props
}) => {
  const t = useTranslations(namespace);

  return (
    <CustomButton 
      variant="contained" 
      startIcon={icon} 
      onClick={onClick} 
      disabled={disabled}
      backgroundColor={backgroundColor}
      hoverBackgroundColor={hoverBackgroundColor}
      color={color}
      borderRadius={borderRadius}
      border={border}
      padding={padding}
      boxShadow={boxShadow}
      hoverBoxShadow={hoverBoxShadow}
      {...props}
    >
      {t(label)}
    </CustomButton>
  );
};

export default MyButton;