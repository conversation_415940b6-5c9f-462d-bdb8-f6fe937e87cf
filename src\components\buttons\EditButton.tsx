import React from "react";
import { Button, ButtonProps } from "@mui/material";
import { useTranslations } from "next-intl";
import EditIcon from "../icons/EditIcon";

interface Props {
  iconOnly?: boolean;
}

const EditButton = (props: Props & ButtonProps) => {
  const t = useTranslations("common");
  const { iconOnly, sx, ...otherProps } = props;

  if (iconOnly) {
    return (
      <Button
        sx={{
          height: 36,
          width: 36,
          borderRadius: 18,
          minWidth: 0,
          ["& .MuiButton-startIcon"]: {
            mx: 0,
          },
          ...sx,
        }}
        variant="contained"
        startIcon={<EditIcon sx={{ height: 16, width: 16 }} />}
        {...otherProps}
      />
    );
  }
  return (
    <Button
      variant="contained"
      endIcon={<EditIcon sx={{ height: 14, width: 14 }} />}
      sx={sx}
      {...otherProps}
    >
      {t("button_edit")}
    </Button>
  );
};

export default EditButton;
