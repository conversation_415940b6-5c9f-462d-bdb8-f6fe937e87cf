"use client"
import { Box, IconButton, Typography } from '@mui/material';
import ContainerTab from './ContainerTab';
import { useEffect, useState } from 'react';
import {EventList} from '@/interface/IEventList';
import xior from "xior";
import Filters from './Filters';

const FeaturedEvent = () =>{

    const [eventList,setEventList] = useState<EventList | undefined>(undefined);
    const [category, setCategory] = useState<string>(''); 
    const [sortOrder, setSortOrder] = useState<string>('desc'); 

    const handleCategoryChange = (category:string) =>{
        setCategory(category);
    }

    const handleSortOrderChange = (sortOrder:string) =>{
      setSortOrder(sortOrder);
    }

    const searchBody = {
        region: '',
        category: category,
        sortOrder: sortOrder,
        page: 1,
      };

    // const getAllEventsApi = async (searchBody: {
    //     region: string;
    //     category: string;
    //     sortOrder: string;
    //     page: number;
    //   }) => {
    //     try {
    //       const response = await xior.get(`/api/featured-event`, {
    //         params: {
    //           region: searchBody.region,
    //           category: searchBody.category,
    //           sortOrder: searchBody.sortOrder,
    //           page: searchBody.page,
    //         },
    //       });
    //       setEventList(response.data);
    //     } catch (error) {
    //       console.error('Error fetching products:', error);
    //     }
    //   };
      
    // useEffect(() => {
    //   getAllEventsApi(searchBody);
    // }, [category,sortOrder]);

    return(
        <>
            <Box sx={{
                paddingLeft:2,
                paddingRight:2,
                mb:2,
                display:"flex",
                justifyContent:"space-between"
            }}>
              <Box>
            <Typography variant="body1" sx={{
                fontSize:"13px",
                color:"rgba(91, 170, 100, 1)"
            }}>體驗 /</Typography>
            <Typography variant="h2">所有活動</Typography>
            </Box>
            <Filters
            category={category}
            handleCategoryChange={handleCategoryChange}
            sortOrder={sortOrder}
            handleSortOrderChange={handleSortOrderChange}
            />
            </Box>
            <Box sx={{
                paddingLeft:2,
                paddingRight:2,
            }}>
                {  eventList &&
                    <ContainerTab
                    eventList={eventList}
                    />
                }
            </Box>
        </>
    )
}

export default FeaturedEvent;