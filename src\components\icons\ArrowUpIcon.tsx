"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const ArrowUpIcon = createSvgIcon(
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_6448_21736)">
    <path d="M17 14L12 9" stroke="#777E90" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M12 9L7 14" stroke="#777E90" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
    <clipPath id="clip0_6448_21736">
    <rect width="24" height="24" fill="white"/>
    </clipPath>
    </defs>
    </svg>,
      "ArrowUp"
);

export default ArrowUpIcon;
