"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const RubbishBinIcon = createSvgIcon(
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_6543_27179)">
            <path d="M5.33325 4.66667L5.33325 3.59064C5.33325 2.84081 5.33325 2.46589 5.5662 2.23294C5.79914 2 6.17406 2 6.9239 2L9.07594 2C9.82578 2 10.2007 2 10.4336 2.23294C10.6666 2.46589 10.6666 2.84081 10.6666 3.59064V4.66667" stroke="#777E90" stroke-width="1.5" />
            <path d="M11.4061 10.9029C11.2388 12.6589 9.76397 14 8 14V14C6.23602 14 4.76117 12.6589 4.59393 10.9029L4 4.66667L12 4.66667L11.4061 10.9029Z" stroke="#777E90" stroke-width="1.5" />
            <path d="M2 4.66699H14" stroke="#777E90" stroke-width="1.5" stroke-linecap="round" />
        </g>
        <defs>
            <clipPath id="clip0_6543_27179">
                <rect width="16" height="16" fill="white" />
            </clipPath>
        </defs>
    </svg>
    ,
    "Rubbish Bin"
);

export default RubbishBinIcon;