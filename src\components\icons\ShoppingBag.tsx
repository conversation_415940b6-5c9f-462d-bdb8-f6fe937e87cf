"use client";
import React from "react";
import { createSvgIcon } from "@mui/material";

const ShoppingBagIcon = createSvgIcon(
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_6543_1326)">
            <path d="M3.33325 7.5H16.6666L15.9986 14.8477C15.905 15.8779 15.0412 16.6667 14.0068 16.6667H5.99302C4.95861 16.6667 4.09488 15.8779 4.00123 14.8477L3.33325 7.5Z" stroke="black" stroke-width="1.5" stroke-linejoin="round" />
            <path d="M6.66675 9.16634V6.66634C6.66675 4.82539 8.15913 3.33301 10.0001 3.33301C11.841 3.33301 13.3334 4.82539 13.3334 6.66634V9.16634" stroke="black" stroke-width="1.5" stroke-linecap="round" />
        </g>
        <defs>
            <clipPath id="clip0_6543_1326">
                <rect width="20" height="20" fill="white" />
            </clipPath>
        </defs>,
    </svg>
    ,
    "Shopping Bag"
);

export default ShoppingBagIcon;