import React, { ChangeEvent } from "react";
import { IconButton, InputAdornment } from "@mui/material";
import TextField from "./TextField";
import { ClearIcon } from "@mui/x-date-pickers";
import debounce from "lodash.debounce";

interface Props {
  value: string;
  onChange: (value: string) => void;
}
const SearchInput = ({ value, onChange }: Props) => {
  const searchRef = React.useRef<HTMLInputElement>();

  const handleKeywordChange = debounce((event: ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.value);
  }, 1000);

  const handleOnClear = () => {
    if (searchRef?.current) {
      searchRef.current.value = "";
      onChange("");
    }
  }

  return (
    <TextField
      inputRef={searchRef}
      sx={{ maxWidth: 450, mb: -1.5, '& input::placeholder':{
        fontSize: '13px', fontWeight:400
      }
      
    }}
      placeholder="Search"
      onChange={handleKeywordChange}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end" >
            <IconButton
              onClick={handleOnClear}
              edge="end"
              disabled={!value}
            >
              <ClearIcon  style={{width:'1.5rem', fontSize: '1.5rem', height:'1.5rem'}}/>
            </IconButton>
          </InputAdornment>
        ),
      }}
    />
  );
};

export default SearchInput;
