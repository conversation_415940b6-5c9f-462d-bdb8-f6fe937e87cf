"use client";
import * as React from "react";
import { MenuItem, Select as MuiSelect, SelectProps } from "@mui/material";
import { useTranslations } from "next-intl";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {
  data: { label: string; value: string }[];
}

export default function Select({
  label,
  description,
  variant = "outlined",
  fullWidth = true,
  error,
  required,
  multiple = false,
  data,
  ...otherProps
}: Props & Omit<SelectProps, "error">) {
  const t = useTranslations("error");

  return (
    <InputContainer label={label} description={description} required={required}>
      <MuiSelect
        multiple={multiple}
        {...otherProps}
        size="small"
      >
        {data.map(({ label, value }) => (
          <MenuItem value={value} key={value}>
            {label}
          </MenuItem>
        ))}
      </MuiSelect>
    </InputContainer>
  );
}
