"use client";
import * as React from "react";
import { TextField as Mui<PERSON>ext<PERSON>ield } from "@mui/material";
import { TextFieldProps } from "@mui/material/TextField";
import InputContainer, { InputContainerProps } from "./InputContainer";

interface Props extends InputContainerProps {}

export default function TextField({
  label,
  labelStyle,
  description,
  variant = "outlined",
  fullWidth = true,
  error,
  required,
  blockStyle,
  ...otherProps
}: Props & Omit<TextFieldProps, "error">) {
  return (
    <InputContainer
      label={label}
      labelStyle={labelStyle}
      blockStyle={blockStyle}
      description={description}
      required={required}
      error={error}
    >
      <MuiTextField
        variant={variant}
        fullWidth={fullWidth}
        error={!!error}
        {...otherProps}
      />
    </InputContainer>
  );
}
