'use client';
import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import Divider from '@mui/material/Divider';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import IconButton from '@mui/material/IconButton';
import { Button, CircularProgress, Grid, Typography } from '@mui/material';
import ShoppingCartItem from './ShoppingCartItem';
import { useEffect, useState } from 'react';
import { RootState, AppDispatch } from '@/redux/stores';
import { increment, decrement} from '@/redux/stores/cartSlice';
import { useSelector, useDispatch } from 'react-redux';
import { useLocale, useTranslations} from 'next-intl';
import {IShoppingCartItem} from '@/interface/IShoppingCartItem';
import {apiClient } from "@/utils/apiClient";
import{ fetchUserData } from "@/redux/userSlice";
import LoadingBackdrop from '../LoadingBackdrop';

interface ShoppingCartProps {
  open: boolean;
  onClose: () => void;
  width?: string | number;
  children?: React.ReactNode;
}

const greenButtonCss = {
  borderColor: 'rgba(230, 232, 236, 1)',
  color: 'rgba(230, 232, 236, 1)',
  backgroundColor: 'rgba(91, 170, 100, 1)',
  '&:hover': {
    borderColor: 'rgba(230, 232, 236, 0.8)',
    backgroundColor: 'rgba(91, 170, 100, 1)',
  },
  '&:focus': {
    borderColor: 'rgba(230, 232, 236, 1)',
  },
  borderRadius: '30px',
  padding: '8px 40px', 
  fontSize: '16px',
  textTransform: 'none',
  borderWidth: '1px',
  borderStyle: 'solid',
  width: '100%', 
  '& .MuiSvgIcon-root': {
    color: 'black',
    marginLeft: '8px', 
    fontSize: '18px',
  }  
}

export default function ShoppingCart({ open, onClose, children, width}: ShoppingCartProps) {

  const t = useTranslations("shopping_cart_component");
  // const quantity = useSelector((state: RootState) => state.cart.quantity);
  const dispatch = useDispatch<AppDispatch>();
  const [shoppingCartItem,setShoppingCartItem] = useState<IShoppingCartItem | undefined>(undefined);
  // const [hasRegisteredToken, setHasRegisteredToken] = useState(false);
  const {userData, loading, error} = useSelector((state: RootState)=> state.userData);
  const [idempotencyKeyDto,setIdempotencyKeyDto] = useState<any>(null);
  const [loadingBackdropOpen,setLoadingBackdropOpen] = useState<boolean>(false);
  const currentLocale = useLocale();
      const localeToLanguageCode: Record<string, string> = {
      en: 'EN',
      zh: 'TC'
    };  // const [hasRegisteredToken, setHasRegisteredToken] = useState(false);
  const languageCode = localeToLanguageCode[currentLocale] || 'EN';


  const getParsedUserToken = () => {
    const userIdToken = localStorage.getItem('userIdToken');
    
    if (!userIdToken) {
      console.log('No user token found');
      return null;
    }
    
    try {
      return JSON.parse(userIdToken);
    } catch (error) {
      console.error('Error parsing user token:', error);
      return null;
    }
  };

  const getShoppingCartItem = async () => {
      try {
          const parsedToken = getParsedUserToken();

          if (!parsedToken) {
              console.log('No user token found');
              return;
          }

          if(userData === null){
              return;
          }

          try {
              const data = await apiClient.get(`/shopping-cart?language=${languageCode}`, parsedToken?.token);
              // if (data?.data?.shoppingCartItems?.length) {
              //   dispatch(initializeQuantity(data.data.shoppingCartItems[0].quantity));
              // }
              if(data?.success){
                  console.log('get cart successful', data);
                  setShoppingCartItem(data);
                  // localStorage.setItem('isRegisteredUser', 'true');
              } else {
                console.log("get cart fail :", data);
              }
          } catch (error) {
              console.error('Error fetching shopping cart items:', error);
          }
      } catch (error) {
          console.error('Error processing user token:', error);
      }
  }
  useEffect(()=>{
      dispatch(fetchUserData())
  },[])
  
  console.log("shoppingCartItem",shoppingCartItem)

  const postGetIdempotencyKey = async () =>{

    const parsedToken = getParsedUserToken();

    if (!parsedToken) {
        console.log('No user token found');
        return;
    }


    try{

      const cartDto = {
        cartType: shoppingCartItem?.data?.shoppingCartType,
        cartId: shoppingCartItem?.data?.shoppingCartId
      }
      const data = await apiClient.post(`/shopping-cart/checkout/idempotency-key`,parsedToken?.token,cartDto);

      if(data.success){
          setIdempotencyKeyDto(data);
          console.log('post idempotency key success',data);
          return data.data?.token; 
      }else if(!data.success){
          console.log("post failed",data)
      }

    }catch(error){
             
        console.error('post idempotency key error:', error);
        throw error;

    }
  }

  // const token = idempotencyKeyDto?.data?.token;

  const handleCheckoutShoppingCart = async (token:string) =>{
    const parsedToken = getParsedUserToken();

    if (!parsedToken) {
        console.log('No user token found');
        return;
    }
    // const token = idempotencyKeyDto?.data?.token;
    try{

      const cartDto = {
        cartType: shoppingCartItem?.data?.shoppingCartType,
        cartId: shoppingCartItem?.data?.shoppingCartId
      }  

      const data = await apiClient.post(
        `/shopping-cart/checkout`,
        parsedToken?.token,
        cartDto,
        {"X-Incutix-Idempotency-Key":token}
      )

      if(data.success){

        console.log("Post shopping checkout success",data.data?.orderId);

        window.location.href = `/order/${data.data?.orderId}`;

      }else if(!data.success){

        console.log("Post shopping checkout failed",data)

      }

    }catch(error){

      console.error('post idempotency key error:', error);
      throw error;

    }

  }

  const handleCheckOutButtonClick = async () => {
    // console.log("Check out click");
    // try {
    //     const idempotencyToken = await postGetIdempotencyKey();
    //     if (idempotencyToken) {
    //           const token = idempotencyKeyDto?.data?.token;
    //         await handleCheckoutShoppingCart(token);
    //     } else {
    //         console.error("Failed to get idempotency token");
    //     }
    // } catch (error) {
    //   console.error("Checkout failed:", error);
    // }
     try {
      const token = await postGetIdempotencyKey(); 
      if (!token) throw new Error("Failed to get token");
      setLoadingBackdropOpen(true)
      await handleCheckoutShoppingCart(token); 
    } catch (error) {
      console.error("Checkout failed:", error);
    } 
  }

  // console.log("idempotencyKeyTokey",idempotencyKeyDto)
  // const token = idempotencyKeyDto?.data?.token;
  // console.log("idempotencyKeyToken token",token);

  const renderCheckOutSection = () =>{

    const itemCount = shoppingCartItem?.data?.summary?.cartTotalQuantity || 0;

    if(shoppingCartItem?.data?.shoppingCartItems?.length !== 0){
        return(
          <>
            <Divider/>
            <Box sx={{mb:2,mt:1,display:"flex",justifyContent:"space-between"}}>
              <Typography sx={{fontSize:"16px"}}>{shoppingCartItem?.data?.summary?.cartTotalQuantity} {t(itemCount === 1 ? 'item' : 'items')}</Typography>
              <Box sx={{display:"flex"}}>
              <Typography>{t('total')} :</Typography>&nbsp;<Typography sx={{fontSize:"24px",color:"rgba(91, 170, 100, 1)"}}>{shoppingCartItem?.data?.currency}{shoppingCartItem?.data?.summary?.cartTotalAmount}</Typography>
              </Box>
              </Box>
              <Button sx={{...greenButtonCss,color:"white"}} onClick={handleCheckOutButtonClick}>{t('checkout_now')}</Button>
              <Box sx={{mt:1}}>
              <Typography sx={{fontSize:"14px",color:"rgba(119, 126, 144, 1)"}}>{t('noticeOne')}</Typography>
              <Typography sx={{fontSize:"14px",color:"rgba(119, 126, 144, 1)"}}>{t('noticeTwo')}</Typography>
              <Typography sx={{fontSize:"14px",color:"rgba(119, 126, 144, 1)"}}>{t('non-refundable')}</Typography>
            </Box>
          </>
        )
    }else{
      return(
        <>
          <Box sx={{
            display:"flex",
            justifyContent:"center",
            alignItems:"center",
            height:"60vh"
          }}>
            <Typography>{t('no_product')}</Typography>
          </Box>
        </>
      )
    }
  }

    useEffect(() => {
        getShoppingCartItem();
    }, [open]);

  return (
    <>
      <Drawer 
        anchor="right" 
        open={open} 
        onClose={onClose}
        ModalProps={{
          BackdropProps: {
            style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' }, 
          },
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: width, 
          },
        }}
      >
        <Box role="presentation" sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <IconButton onClick={onClose} aria-label="close">
              <HighlightOffIcon />
            </IconButton>
          </Box>
            <Typography sx={{mb:2,fontSize:"24px"}}>{t('shopping_cart')}</Typography>
            <Divider/>
            <Box sx={{ 
                maxHeight: '60vh', 
                overflowY: 'auto', 
              }}>
              {
                shoppingCartItem ? (
                  <ShoppingCartItem
                  shoppingCartItem={shoppingCartItem}
                  getShoppingCartItem={getShoppingCartItem}
                  />) : (                  
                  <Grid container justifyContent="center" alignItems="center" 
                    sx={{
                      height:"50vh",
                      marginRight:{md:"400px"}
                    }}
                    >
                      <CircularProgress sx={{color:"#ff7802"}}/>
                  </Grid>)
              }
            </Box>
              {renderCheckOutSection()}
            <LoadingBackdrop open={loadingBackdropOpen}/>
        </Box>
      </Drawer>
    </>
  );
}
