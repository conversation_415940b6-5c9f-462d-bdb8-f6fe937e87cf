'use client';
import { Box, Button, IconButton, Paper, Snackbar, Typography } from '@mui/material';
import * as React from 'react';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Image from "next/image";
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import type { IShoppingCartItem } from '@/interface/IShoppingCartItem';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { apiClient } from "@/utils/apiClient";
import { useState } from 'react';
import CustomSnackbar from '@/components/Snackbar';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import { useTranslations } from 'next-intl';
import { useDispatch } from "react-redux";
import { fetchCartCount } from "@/redux/cartCountSlice";
import { AppDispatch } from '@/redux/store';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RubbishBinIcon from '../icons/RubbishBinIcon';

interface Props {
    shoppingCartItem: IShoppingCartItem;
    getShoppingCartItem: () => void;
}

const ShoppingCartItem = ({ shoppingCartItem, getShoppingCartItem }: Props) => {

    const [openSnackBar, setOpenSnackBar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState("");
    const t = useTranslations("shopping_cart_component");
    const dispatch = useDispatch<AppDispatch>();
    const handleSnackBarClose = () => {
        setOpenSnackBar(false);
    };

    const iconButtonStyle = {
        border: "2px solid rgba(244, 245, 246, 1)",
        width: "30px",
        height: "30px",
        borderRadius: "999px",
        color: "rgba(255, 255, 255, 1)"
    }

    const getUserIdToken = () => {
        const userIdToken = localStorage.getItem('userIdToken');
        if (!userIdToken) {
            console.log('No user token found');
            return null;
        }
        return JSON.parse(userIdToken);
    }

    const handleQuantityMinusOne = async (productId: number, skuId: number, quantity: number) => {

        try {

            if (quantity > 1) {
                const parsedToken = getUserIdToken();
                if (!parsedToken) return;

                const updateCartItemDto = {
                    cartItemType: "product",
                    productId: productId,
                    skuId: skuId,
                    quantity: quantity - 1
                }

                const data: any = await apiClient.put(`/shopping-cart`, parsedToken?.token, updateCartItemDto);

                if (data.success) {
                    console.log("update item delete successfully");
                    getShoppingCartItem();
                    dispatch(fetchCartCount());
                }
            }

        } catch (error) {
            console.error('Error update shopping cart items:', error);
        }
    }

    const handleQuantityPlusOne = async (productId: number, skuId: number, quantity: number) => {

        try {

            const parsedToken = getUserIdToken();
            if (!parsedToken) return;

            const updateCartItemDto = {
                cartItemType: "product",
                productId: productId,
                skuId: skuId,
                quantity: quantity + 1
            }

            const data = await apiClient.put(`/shopping-cart`, parsedToken?.token, updateCartItemDto);

            if (data.success) {
                console.log("update item delete successfully");
                getShoppingCartItem();
                dispatch(fetchCartCount());
            } else if (!data.success) {
                if (data?.error?.code.includes("CART_7005")) {
                    setOpenSnackBar(true);
                    setSnackbarMessage(t('out_of_stock'));
                }
            }
            console.log('data', data)
        } catch (error) {
            console.error('Error update shopping cart items:', error);
        }
    }

    const deteleShoppingCartItem = async (productId: number, skuId: number) => {
        try {

            const parsedToken = getUserIdToken();
            if (!parsedToken) return;

            const deleteCartItemDto = {
                cartItemType: "product",
                productId: productId,
                skuId: skuId,
            }
            // console.log("deleteCartItemDto",deleteCartItemDto)
            const data = await apiClient.delete(`/shopping-cart`, parsedToken?.token, deleteCartItemDto)
            console.log("deleteCartItemDto data", data)
            if (data.success) {
                console.log("cart item delete successfully")
                getShoppingCartItem()
                setOpenSnackBar(true);
                setSnackbarMessage(t('products_remove'));
                dispatch(fetchCartCount());
            }

        } catch (error) {
            console.error('Error detele shopping cart items:', error);
        }
    }

    const renderStockStatusChip = (value: any) => {
        if (!value.inStock) {
            return (
                <Chip
                    label={t('sold_out')}
                    variant="outlined"
                    icon={<ErrorOutlineIcon />}
                    sx={{
                        color: "rgba(245, 34, 45, 1)",
                        borderColor: "rgba(255, 245, 245, 1)",
                        backgroundColor: "rgba(255, 245, 245, 1)",
                        borderRadius: "999px",
                        "& .MuiChip-icon": {
                            color: "rgba(245, 34, 45, 1)",
                            fontSize: "16px"
                        },
                    }}
                />
            )
        } else if (!value.isAvailable) {
            return (
                <Chip
                    label={t('off_the_shelf')}
                    variant="outlined"
                    icon={<ErrorOutlineIcon />}
                    sx={{
                        color: "rgba(245, 34, 45, 1)",
                        borderColor: "rgba(255, 245, 245, 1)",
                        backgroundColor: "rgba(255, 245, 245, 1)",
                        borderRadius: "999px",
                        "& .MuiChip-icon": {
                            color: "rgba(245, 34, 45, 1)",
                            fontSize: "16px"
                        },
                    }}
                />
            )
        } else if (value.remainStock <= 6) {
            return (
                <Chip
                    label={`${t('remain')} ${value.remainStock}`}
                    variant="outlined"
                    icon={<ErrorOutlineIcon />}
                    sx={{
                        color: "rgba(245, 130, 32, 1)",
                        borderColor: "rgba(255, 240, 228, 1)",
                        backgroundColor: "rgba(255, 240, 228, 1)",
                        borderRadius: "999px",
                        "& .MuiChip-icon": {
                            color: "rgba(245, 130, 32, 1)",
                            fontSize: "16px"
                        },
                    }}
                />
            )
        }

        else {
            return null;
        }
    }

    const renderShoppingCartItem = () => {
        return (
            <>
                {
                    shoppingCartItem &&
                    shoppingCartItem?.data?.shoppingCartItems.map((value) => {
                        // const skuAttributes = getProductSKUAttributes(value.skuId);
                        const attributeText = value?.skuAttribute?.map((attr: any) =>
                            `${attr.category.charAt(0).toUpperCase() + attr.category.slice(1)}: ${attr.value}`
                        ).join(', ');
                        // return (<>
                        return (<React.Fragment key={value.shippingCartItemId}>
                            <Box sx={{ display: "flex", gap: 2 }}>
                                <img src={`${value.thumbnail}`} width="60px" height="60px"></img>
                                <Box>
                                    <Typography sx={{ fontSize: "14px" }}>{value.productName}</Typography>
                                    <Typography sx={{ fontSize: "12px", color: "rgba(119, 126, 144, 1)", mb: 2 }}>{attributeText || ''}</Typography>
                                </Box>
                            </Box>
                            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mt: 1, mb: 1 }}>
                                <Typography sx={{ fontSize: "14px" }}>{shoppingCartItem?.data?.currency} {value.price.salePrice}</Typography>
                                <Box sx={{ display: "flex", gap: 1 }}>
                                    {renderStockStatusChip(value)}
                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                        <Box sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            border: "1px solid rgba(244, 245, 246, 1)",
                                            borderRadius: "999px"
                                        }}>
                                            <IconButton
                                                onClick={() => handleQuantityMinusOne(value.productId, value.skuId, value.quantity)}
                                                size="small"
                                                disabled={value.quantity <= 1}
                                                sx={iconButtonStyle}
                                            >
                                                <RemoveIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                            </IconButton>
                                            <Typography
                                                variant="body1"
                                                sx={{ px: 2, minWidth: '40px', textAlign: 'center' }}
                                            >
                                                {/* {quantity} */}
                                                {value.quantity}
                                            </Typography>
                                            <IconButton
                                                onClick={() => handleQuantityPlusOne(value.productId, value.skuId, value.quantity)}
                                                size="small"
                                                sx={iconButtonStyle}
                                            >
                                                <AddIcon sx={{ color: "rgba(91, 170, 100, 1)" }} />
                                            </IconButton>
                                        </Box>
                                        <IconButton sx={iconButtonStyle} onClick={() => deteleShoppingCartItem(value.productId, value.skuId)}>
                                            {/* <DeleteOutlineIcon sx={{
                                                width: "16px",
                                                height: "16px",
                                                top: "7px",
                                                left: "7px",
                                                color: "rgba(119, 126, 144, 1)"
                                            }} /> */}
                                            <RubbishBinIcon
                                                sx={{
                                                    width: "16px",
                                                    height: "16px",
                                                    top: "7px",
                                                    left: "7px",
                                                    color: "rgba(119, 126, 144, 1)"
                                                }}
                                            />
                                        </IconButton>
                                    </Box>
                                    <CustomSnackbar
                                        open={openSnackBar}
                                        onClose={handleSnackBarClose}
                                        autoHideDuration={2000}
                                        message={snackbarMessage}
                                        color='rgba(245, 34, 45, 1)'
                                        backgroundColor='rgba(255, 245, 245, 1)'
                                    />
                                </Box>
                            </Box>
                            {value.inStock ? "" : <Typography sx={{
                                fontSize: "14px",
                                color: "rgba(245, 34, 45, 1)",
                                mb: 2
                            }}>{t('sold_out_warnings')}</Typography>}
                            {value.isAvailable ? "" : <Typography sx={{
                                fontSize: "14px",
                                color: "rgba(245, 34, 45, 1)",
                                mb: 2
                            }}>{t('off_the_shelf_warnings')}</Typography>}
                            {/* </>) */}
                        </React.Fragment>)
                    })
                }
            </>
        )
    }

    const renderShoppingCartType = () => {
        if (shoppingCartItem?.data?.shoppingCartType.includes("productOnly")) {
            return (
                <>
                    <Typography sx={{ fontSize: "13px" }}>{t('products')}</Typography>
                </>
            )
        }
    }

    const renderRegionName = () => {
        if (shoppingCartItem?.data?.regionName) {
            return (
                <>
                    <Typography sx={{ fontSize: "13px" }}>{shoppingCartItem?.data?.regionName}</Typography>
                </>
            )
        }
    }

    const renderEventName = () => {
        if (shoppingCartItem?.data?.shoppingCartItems.length !== 0) {
            return (
                <>
                    <Box sx={{ display: 'flex' }}>
                        <Typography variant='body1' sx={{ fontSize: "14px", mb: 2, mr: 1 }}>{shoppingCartItem?.data?.events?.[0]?.eventName || ""}</Typography>
                        <Stack direction="row" spacing={1}>
                            <Chip label={renderRegionName()} sx={{ color: "rgba(91, 170, 100, 1)", backgroundColor: "rgba(240, 251, 239, 1)", borderColor: "rgba(240, 251, 239, 1)" }} />
                            <Chip label={renderShoppingCartType()} sx={{ color: "rgba(91, 170, 100, 1)", backgroundColor: "rgba(240, 251, 239, 1)", borderColor: "rgba(240, 251, 239, 1)" }} />
                        </Stack></Box>
                </>
            )
        }
    }

    return (
        <>
            <Box sx={{ mt: 2 }}>
                {renderEventName()}
                {renderShoppingCartItem()}
            </Box>
        </>
    )
}

export default ShoppingCartItem;