// useUserData.ts
import { useState, useEffect } from 'react';

interface UserData {
  userId: string;
  nickName: string;
  email: string;
}

export const useUserData = () => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {

        const jwtToken = process.env.NEXT_PUBLIC_JWT_TOKEN;
        const userIdToken = localStorage.getItem('userIdToken');

        if (!jwtToken || !userIdToken) {
          throw new Error('Authentication tokens are missing');
        }

        // const storedItem = JSON.parse(userIdToken);

        // const storedItem = localStorage.getItem('userIdToken');
        // if (storedItem) {
        //   const parsedItem = JSON.parse(storedItem);
        //   const now = new Date();
          
        //   if (now.getTime() > parsedItem.expiry) {
        //     // token已過期，移除
        //     localStorage.removeItem('userIdToken');
        //     console.log('Token已過期，已移除');
        //   } else {
        //     // token有效
        //     console.log('Token有效', parsedItem.value);
        //     // 可以在這裡使用token...
        //   }
        // }

        let parsedItem;
          try {
            parsedItem = JSON.parse(userIdToken);
          } catch (parseError) {
            throw new Error('Failed to parse userIdToken');
          }

          const now = new Date();
          if (now.getTime() > parsedItem.expiry) {
            localStorage.removeItem('userIdToken');
            throw new Error('Token has expired');
          }

        const response = await fetch(`https://mock-api.incutix.com/v1/application/user/info?id=${parsedItem.token}`, {
          headers: { 
            'X-Ix-Application-jwt': jwtToken,
            'Content-Type': 'application/json'
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.data) {
          throw new Error('Invalid user data received from server');
        }

        // localStorage.setItem('cachedUserData', JSON.stringify(data.data));
        localStorage.removeItem('cachedUserData');
        setUserData(data.data);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  //   useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       // 模擬 API 呼叫延遲
  //       await new Promise(resolve => setTimeout(resolve, 500));
        
  //       // 直接返回測試數據
  //       const mockUserData: UserData = {
  //         userId: '123',
  //         nickName: '測試用戶',
  //         email: '<EMAIL>'
  //       };
        
  //       setUserData(mockUserData);
        
  //     } catch (err) {
  //       setError(err instanceof Error ? err.message : 'An unknown error occurred');
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchData();
  // }, []);


  const refresh = () => {
    // localStorage.removeItem('cachedUserData');
    setLoading(true);
    setError(null);
    // useEffect(() => fetchData(), []);
  };

  return { userData, loading, error, refresh };
};