export interface IOrderItem {
    success: boolean;
    data:    Data;
}

export interface Data {
    orderId:        number;
    orderIsAvailable: boolean;
    userId:         number;
    orderType:      string;
    region:         string;
    currency:       string;
    orderStatus:    string;
    orderAdminNote: string;
    orderUserNote:  string;
    orderExpireAt:  number;
    orderSummary:   OrderSummary;
    paymentMethod:  PaymentMethod[];
    events:         Event[];
    orderItems:     OrderItem[];
    orderContact:   Order;
    orderShipping:  Order;
    orderBilling:   Order;
}

export interface Event {
    event_id:  number;
    eventName: string;
}

export interface Order {
    name:        string;
    countryCode: string;
    tel:         string;
    address?:    string;
    email?:      string;
}

export interface OrderItem {
    orderItemId:  number;
    productId:    number;
    skuId:        number;
    productName:  string;
    thumbnail:    string;
    price:        Price;
    skuAttribute: SkuAttribute[];
    quantity:     number;
}

export interface Price {
    unitPrice:     number;
    originalPrice: number;
    totalPrice:    number;
}

export interface SkuAttribute {
    category: string;
    value:    string;
}

export interface OrderSummary {
    orderTotalQuantity:  number;
    orderTotalAmount:    number;
    orderSubTotal:       number;
    orderShippingFee:    number;
    orderTaxAmount:      number;
    orderDiscountAmount: number;
}

export interface PaymentMethod {
    paymentCode:     string;
    paymentPlatform: string;
    paymentType:     string;
}