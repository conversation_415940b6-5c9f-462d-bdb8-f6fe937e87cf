import { ITag } from "./ITag";

export interface IProduct {
    id?: number;
    name: string;
    description: string;
    thumbnail: string;
    audio?: string;
    owner: string;
    tags:Tags[];
    collections: Collection[];
    // createdDate: number;
    // updatedDate: number;
    updatedAt?: number;
    createdAt?: number;
    status?: number;
    price?: number;
    compareAt:number;
    tax: number;
    type?: number;
    quantity?:number;
    roomExcluded:string;
    productType: number;
    openToAllMembers:boolean;
    productImage?: ProductImage[];
    currency?: string;
    weight?: number;
    weightUnit?: string;
    category?:number;
    isDigital?: boolean;
    userId?:    number;
    taxIncluded?:boolean;
    memberGroupId?:   string[];
    exclusiveId?:     string[];
    productId?:       string[];
    memberGroupNames?: string[];
    memberGroup: MemberGroup[];
}

export interface ProductImage {
    name: string;
    url: string;
    resourceType: string;
}

export interface Tags {
    id:number;
    name:string;
    userId:string;
}

export interface MemberGroup {
    id: number;
    name: string;
    userId:number;
}

export interface Collection{
    id:number;
    name:string;
    description:string;
    photoUrl:string;
    createdAt: number;
    updatedAt:number;
    userId:number;
}

// export interface IOwner {
//     countryCode:string;
//     createdAt:number;
//     email:string;
//     gender:string;
//     id:number;
//     introduction:string;
//     name:string;
//     ownerId:string;
//     photoUrl:string;
//     userId:number;
// }
