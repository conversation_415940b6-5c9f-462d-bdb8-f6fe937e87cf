export interface ProductDetailsDto {
    item: Item;
}

export interface Item {
    id:               number;
    name:             string;
    description?:      string;
    thumbnail:        string;
    audio:            string;
    owner:            number;
    createdAt:        number;
    updatedAt:        number;
    status:           number;
    price:            string;
    taxIncluded:      number;
    tax:              string;
    type:             number;
    currency:         "USD" | "HKD" | "EUR" | "GBP" | "JPY" | "KRW";
    weight:           string;
    weightUnit:       string;
    isDigital:        number;
    openToAllMembers: number;
    category:         number;
    compareAt:        string;
    productType:      number;
    roomExcluded:     string;
    collections:      string;
    tags:             string;
    userId:           number;
}
