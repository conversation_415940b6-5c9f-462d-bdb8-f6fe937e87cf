export interface ISearchCriteria {
    success: boolean;
    data:    Data;
}

export interface Data {
    events:            Event[];
    productAttributes: ProductAttribute[];
    region:            Region;
}

export interface Event {
    eventId:   number;
    eventName: string;
}

export interface ProductAttribute {
    category: string;
    value:    string[];
}

export interface Region {
    HK?: Hk;
    MY?: Hk;
}

export interface Hk {
    countryCode: string;
    name:        string;
}
