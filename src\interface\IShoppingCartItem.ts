export interface IShoppingCartItem {
    success: boolean;
    data:    Data;
}

export interface Data {
    shoppingCartId:    number;
    shoppingCartType:  string;
    region:            string;
    regionName?:       string;
    currency?:         string;
    summary:           Summary;
    shoppingCartItems: ShoppingCartItem[];
    events:            EventElement[];
}

export interface EventElement {
    eventId:   number;
    eventName: string;
}

export interface ShoppingCartItem {
    shippingCartItemId:   number;
    shippingCartItemType: string;
    is_selected:          boolean;
    productId:            number;
    skuId:                number;
    productName:          string;
    thumbnail:            string;
    event:                ShoppingCartItemEvent;
    price:                Price;
    skuAttribute?:         SkuAttribute[];
    quantity:             number;
}

export interface ShoppingCartItemEvent {
    id:   number;
    name: string;
}

export interface Price {
    salePrice:     number;
    originalPrice: number;
}

export interface Summary {
    cartTotalQuantity: number;
    cartTotalAmount:   number;
}

export interface SkuAttribute {
    category: string;
    value:    string;
}