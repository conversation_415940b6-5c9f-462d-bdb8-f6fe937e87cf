export interface Root {
    success: boolean
    data: Data
  }
  
  export interface Data {
    items: Item[]
    meta: Meta
  }
  
  export interface Item {
    productId:     number;
    productName:   string;
    thumbnail:     string;
    region:        string;
    currency?:      string;
    saleStartDate?: number;
    saleEndDate?:   number;
    country:       Country;
    event:         Event;
    price:         number;
    inventory?:     Inventory;
    isAvailable?:   boolean;
}

export interface Country {
    code: string;
    name: string;
}

export interface Event {
    id:   number;
    name: string;
}

export interface Inventory {
    stock: number;
}

export interface Meta {
    page:  number;
    size:  number;
    total: number;
}