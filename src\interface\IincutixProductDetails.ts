export interface IincutixProductDetails {
    success: boolean;
    data:    Data;
}

export interface Data {
    productId:           number;
    skuId:               number;
    productName:         string;
    description:         string;
    productIntroduction: string;
    saleStartDate:       number;
    salesEndDate:        number;
    category:            number;
    currency?:           string;
    country:             Country;
    price:               Price;
    event:               Event;
    media:               Media;
    shipping:            Shipping;
    isAvailable:         boolean;
    productAttributes:   ProductAttributes;
    productSKU:          ProductSKU[];
}

export interface Country {
    code: string;
    name: string;
}

export interface Event {
    id:   number;
    name: string;
}

export interface Media {
    image: Image[];
    video: any[];
}

export interface Image {
    isMain:   boolean;
    path:     string;
    priority: number;
}

export interface Price {
    originalPrice: number;
    salePrice:     number;
}

export interface ProductAttributes {
    default:       Default[];
    skuAttributes: { [key: string]: Default[] };
    attributes:    Attribute[];
}

export interface Attribute {
    category: Category;
    value:    string[];
}

export enum Category {
    Color = "color",
    Size = "size",
}

export interface Default {
    category: Category;
    value:    string;
}

export interface ProductSKU {
    skuId:        number;
    price:        Price;
    media:        Media;
    skuAttribute: Default[];
    isAvailable:  boolean;
}

export interface Shipping {
    pickupStartDate: number;
    pickupEndDate:   number;
    shippingMethod:  string[];
    pickupVenue:     string;
}
