// cartCountSlice.ts
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiClient } from "@/utils/apiClient"; // Make sure you have this import

export const fetchCartCount = createAsyncThunk(
  "cart/fetchCartCount",
  async (_, { rejectWithValue }) => {
    try {
      const userIdToken = localStorage.getItem("userIdToken");

      if (!userIdToken) {
        console.log("No user token found");
        return rejectWithValue("No user token found");
      }

      const parsedToken = JSON.parse(userIdToken);
      const response = await apiClient.get(
        "/shopping-cart/count",
        parsedToken?.token
      );

      return response.data; // Assuming apiClient returns the response directly
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Unknown error");
    }
  }
);

interface CartCountState {
  isLoading: boolean;
  data: {
    count: number; // 確保這裡有 count 屬性
  };
  error: string | null;
  shoppingCartOpen: boolean;
}

const initialState: CartCountState = {
  isLoading: false,
  data: {
    count: 0, // 初始值
  },
  error: null,
  shoppingCartOpen: false,
};

const cartCountSlice = createSlice({
  name: "cartCount",
  initialState,
  // reducers: {}, // Add any reducers if needed
  reducers: {
    openShoppingCart: (state) => {
      state.shoppingCartOpen = true;
    },
    closeShoppingCart: (state) => {
      state.shoppingCartOpen = false;
    },
  }, // Add any reducers if needed
  extraReducers: (builder) => {
    builder
      .addCase(fetchCartCount.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCartCount.fulfilled, (state, action) => {
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(fetchCartCount.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});
export const { openShoppingCart, closeShoppingCart } = cartCountSlice.actions;
export const cartCountReducer = cartCountSlice.reducer;