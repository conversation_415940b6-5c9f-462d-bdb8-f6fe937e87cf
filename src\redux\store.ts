// store.ts
import { configureStore } from "@reduxjs/toolkit";
import { cartCountReducer } from "./cartCountSlice";
import { userDataReducer} from "./userSlice"

export const store = configureStore({
  reducer: {
    cartCount: cartCountReducer,
    userData: userDataReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // Adjust based on your needs
    }),
});

// Export RootState type
export type RootState = ReturnType<typeof store.getState>;

// Export AppDispatch type
export type AppDispatch = typeof store.dispatch;