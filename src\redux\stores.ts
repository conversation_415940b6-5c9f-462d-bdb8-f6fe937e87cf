import { Action, AnyAction, combineReducers, configureStore, ThunkAction } from "@reduxjs/toolkit";
import { commonReducer } from "./stores/commonSlice";
import { createWrapper, HYDRATE } from "next-redux-wrapper";
import cartReducer from './stores/cartSlice';
export interface State {
    common: any;
}

const combinedReducer = combineReducers({
    common: commonReducer,
    cart: cartReducer,
  });
  
  const reducer = (state: ReturnType<typeof combinedReducer>, action: AnyAction) => {
    if (action.type === HYDRATE) {
      const nextState = {
        ...state, // use previous state
        ...action.payload, // apply delta from hydration
      };
      return nextState;
    } else {
      return combinedReducer(state, action);
    }
};

export const makeStore = () => 
    configureStore({
      reducer,
    });

type Store = ReturnType<typeof makeStore>;

export type AppDispatch = Store['dispatch'];
export type RootState = ReturnType<Store['getState']>;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;

export const wrapper = createWrapper(makeStore, { debug: true });