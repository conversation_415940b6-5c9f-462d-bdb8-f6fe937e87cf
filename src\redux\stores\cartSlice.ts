import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const cartSlice = createSlice({
  name: 'cart',
  initialState: {
    quantity: 1,  
  },
  reducers: {
    increment: (state) => {
      state.quantity += 1;
    },
    decrement: (state) => {
      if (state.quantity > 1) {
        state.quantity -= 1;
      }
    },
    initializeQuantity: (state, action: PayloadAction<number>) => {
      state.quantity = action.payload;
    },
  },
});

export const { increment, decrement,initializeQuantity } = cartSlice.actions;
export default cartSlice.reducer;