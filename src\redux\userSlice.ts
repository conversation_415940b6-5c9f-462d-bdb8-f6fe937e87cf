import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import xior from "xior";
interface UserData {
  userId: string;
  nickName: string;
  email: string;
}

interface UserState {
  userData: UserData | null;
  loading: boolean;
  error: string | null;
}

interface TokenResponse {
  result: boolean;
  data: {
    jwt: string;
    expirationTime: number;
  };
}

const initialState: UserState = {
  userData: null,
  loading: false,
  error: null,
};

// Async thunk for token exchange
export const exchangeToken = createAsyncThunk(
  'user/exchangeToken',
  async (_, { rejectWithValue }) => {
    try {
      const appKey = process.env.NEXT_PUBLIC_APPLICATION_KEY;
      if (!appKey) {
        throw new Error('Application key is missing');
      }

      const response = await fetch(
        'https://dev-api.incutix.com/v1/application/token/exchange',
        {
          method: 'POST',
          headers: {
            'X-Ix-Application-Key': appKey,
            'Content-Type': 'application/json',
          },
        }
      );


      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Token exchange failed! status: ${response.status}`);
      }

      const data: TokenResponse = await response.json();
      if (!data.result || !data.data?.jwt) {
        throw new Error('Invalid token response received from server');
      }

      return data.data.jwt;
    } catch (err) {
      return rejectWithValue(err instanceof Error ? err.message : 'Token exchange failed');
    }
  }
);

// Modified fetchUserData to use the exchanged token
export const fetchUserData = createAsyncThunk(
  'user/fetchData',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // First exchange the token
      const jwtToken = await dispatch(exchangeToken()).unwrap();
      
      const userIdToken = localStorage.getItem('userIdToken');
      if (!userIdToken) {
        throw new Error('User ID token is missing');
      }

      let parsedItem;
      try {
        parsedItem = JSON.parse(userIdToken);
      } catch (parseError) {
        throw new Error('Failed to parse userIdToken');
      }

      const response = await fetch(
        `https://dev-api.incutix.com/v1/application/user/info?id=${parsedItem.token}`,
        {
          headers: {
            'X-Ix-Application-jwt': jwtToken,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (!data.data) {
        throw new Error('Invalid user data received from server');
      }

      localStorage.removeItem('cachedUserData');
      return data.data;
    } catch (err) {
      return rejectWithValue({message: err instanceof Error ? err.message : 'An unknown error occurred',
  originalError: err});
      
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserData.fulfilled, (state, action) => {
        state.loading = false;
        state.userData = action.payload;
      })
      .addCase(fetchUserData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const userDataReducer = userSlice.reducer;