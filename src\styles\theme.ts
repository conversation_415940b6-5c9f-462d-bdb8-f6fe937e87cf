"use client";
import { DM_Sans } from "next/font/google";
import { createTheme } from "@mui/material/styles";
import { COLORS } from "./colors";

const dmSans = DM_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
});

const theme = createTheme({
  typography: {
    fontFamily: dmSans.style.fontFamily,
  },
  palette: {
    primary: {
      main: "#24378C",
    },
    secondary: {
      main: "#E7E7E7",
    },
    text: {
      primary: "#272727",
      secondary: "#ACACAC",
    },
  },
  components: {
    MuiButton: {
      defaultProps: {
        disableElevation: true,
      },
      styleOverrides: {
        root: {
          textTransform: "none",
        },
        contained: {
          borderRadius: 5,
        },
        outlined: {
          fontSize: 10,
          fontWeight: "700",
          borderRadius: 8,
          borderColor: "#D9D9D9",
          color: "#ACACAC",
        },
        colorSecondary: "#E7E7E7",
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          color: "#000",
          "&.Mui-selected": {
            backgroundColor: COLORS.BLUE,
            color: COLORS.WHITE,
            svg: {
              color: COLORS.WHITE,
            },
          },
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          width: 14,
          height: 14,
          minWidth: "0 !important",
          marginRight: 12,
          svg: {
            color: "#000",
          },
          "&.Mui-selected": {
            color: COLORS.WHITE,
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        head: {
          height: 50,
          background: COLORS.BLUE,
        },
        root: {
          height: 68,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          color: COLORS.WHITE,
          fontWeight: 700,
          fontSize: 13,
          textAlign: "center",
          border: "1px solid #B9B9B9",
        },
        body: {
          color: "#000",
          fontSize: 13,
          fontWeight: 400,
          border: "1px solid #B9B9B9",
          textAlign: "center",
        },
      },
    },
    MuiPaginationItem: {
      styleOverrides: {
        root: {
          height: 27,
          width: 27,
          border: "0.5px solid #777777",
          borderRadius: 5,
          color: "#000",
          fontSize: 13,
          fontWeight: 300,
          padding: 0,
          minWidth: 0,
          "&.Mui-selected": {
            background: COLORS.BLUE,
            color: COLORS.WHITE,
          },
        },
        ellipsis: {
          borderWidth: 0,
          fontSize: 24,
          color: COLORS.BLUE,
          boxShadow: "unset",
        },
        // previousNext: {
        //   color: COLORS.GRAY,
        // },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          height: 26,
          border: "1px solid #24378C",
        },
        label: {
          paddingLeft: 16,
          paddingRight: 16,
          fontSize: 13,
          fontWeight: 300,
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 56,
          height: 26,
          padding: 0,
        },
        switchBase: {
          padding: 0,
          margin: 2,
          transitionDuration: "300ms",
          "&.Mui-checked": {
            transform: "translateX(28px)",
            color: "#fff",
            "& + .MuiSwitch-track": {
              backgroundColor: COLORS.BLUE,
              opacity: 1,
              border: 0,
            },
            "&.Mui-disabled + .MuiSwitch-track": {
              opacity: 0.5,
            },
          },
          "&.Mui-focusVisible .MuiSwitch-thumb": {
            color: COLORS.BLUE,
            border: "6px solid #fff",
          },
          "&.Mui-disabled .MuiSwitch-thumb": {
            color: "#777777",
          },
          "&.Mui-disabled + .MuiSwitch-track": {
            opacity: 0.3,
          },
        },
        thumb: {
          boxSizing: "border-box",
          width: 22,
          height: 22,
        },
        track: {
          borderRadius: 26 / 2,
          backgroundColor: "#777",
          opacity: 1,
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        size: "small",
      },
      styleOverrides: {
        root: {},
      },
    },
    MuiInputBase: {
      styleOverrides: {
        sizeSmall: {
          "& .MuiOutlinedInput-notchedOutline": {
            borderWidth: "0.5px",
            borderColor: "#000",
            borderRadius: 10,
          },
        },
      },
    },
    MuiModal: {
      styleOverrides: {
        backdrop: {
          backgroundColor: "unset",
        },
      },
    },
  },
});

theme.typography.body2 = {
  // fontSize: "0.75rem", // 12px
  fontWeight: 500,
  fontSize: "16px"
};
theme.typography.body1 = {
  // fontSize: "0.875rem", //14px
  fontWeight: 500,
  fontSize: "18px"
};
theme.typography.h5 = {
  fontSize: "1rem", //16px
  fontWeight: 500,
};
theme.typography.h4 = {
  fontSize: "1.125rem", //18px
  fontWeight: 500,
};
theme.typography.h3 = {
  fontSize: "1.25rem", //20px
  fontWeight: 600,
};
theme.typography.h2 = {
  fontSize: "1.5rem", //24px
  fontWeight: 600,
};
theme.typography.h1 = {
  color: "#272727",
  fontSize: "2.25rem", //24px
  fontWeight: 700,
};

export default theme;
