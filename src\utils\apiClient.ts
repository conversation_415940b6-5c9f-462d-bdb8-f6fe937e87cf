const getFullUrl = (endpoint: string) => {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE || '';
  const urlPrefix = process.env.NEXT_PUBLIC_URL_PREFIX || '';
  return `${baseUrl}${urlPrefix}${endpoint}`;
};

export const apiClient = {
  get: async (endpoint: string, token?: string) => {
    // const token = getToken();
    const url = getFullUrl(endpoint);
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.json();
  },

  post: async (endpoint: string, token?: string, data?: any,headers: Record<string, string> = {}) => {
    // const token = getToken();
    const url = getFullUrl(endpoint);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
         ...headers
      },
      body: JSON.stringify(data)
    });
    return response.json();
  },

  put: async (endpoint: string, token?: string, data?: any) => {
    // const token = getToken();
    const url = getFullUrl(endpoint);
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  },

  delete: async (endpoint: string, token?: string, data?: any) => {
    const url = getFullUrl(endpoint);
    const config: RequestInit = {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    };
    
    if (data) {
      config.headers = {
        ...config.headers,
        'Content-Type': 'application/json'
      };
      config.body = JSON.stringify(data);
    }
    
    const response = await fetch(url, config);
    return response.json();
  }
};