export const ROUTES = {
  HOME: "/home",
  LOGIN: "/login",
  SIGN_UP: "/sign-up",
  RESET_PASSWORD: "/reset-password/[token]",
  FORGOT_PASSWORD: "/forgot-password",
  VERIFY: "/verify",
  SETUP_PROFILE: "/setup-profile",

  DASHBOARD: "/dashboard",
  PRODUCT: "/product",
  PRODUCTS: "/products",
  PRODUCT_LIST:"/products/product-list",
  // INVENTORY_ITEM_EDIT: (id:string) =>  `/products/inventory/${id}`,
  ADJUSTMENT_HISTORY: (id:string) =>  `/products/inventory/${id}/history`,
  INVENTORY:"/products/inventory",
  CREATE_PRODUCT: "/product/create",
  CREATE_PRODUCTS: "/products/product-list/create",
  ROOMS: "/room-information",
  CREATE_ROOM: "/room-information/create",
  SALES_ORDER: "/sales-orders",
  DELIVERY_NOTE:"/sales-orders/delivery-note",
  DELIVERY_NOTE_EDIT: (noteId:string) => `/sales-orders/delivery-note/${noteId}/edit`,
  ORDER_LIST:"/sales-orders/order-list",
  MEMBER_MANAGEMENT: "/member-management",
  MEMBER_LIST: "/member-management/list",
  MEMBER_GROUP: "/member-management/group",
  INVITATION: "/member-management/invitation",
  MESSAGE: "/message",
  PRODUCT_CATEGORY: "/product-category",
  COLLECTION: "/collections",
  CREATE_COLLECTION: "/collections/create",
  OWNER: "/owners",
  CREATE_OWNER: "/owners/create",
  TAGS: "/tags",
  SETTINGS: "/settings",
  PROFILE: "/settings/profile",
  CHANGE_PASSWORD: "/settings/change-password",
  ACCOUNT_SETTINGS: "/account-settings"
};

export const SCHEMA_ERRORS = {
  required: "required",
  invalidEmail: "invalid_email",
  invalidPassword: "invalid_password",
  unmatchPassword: "unmatch_password",
  invalidImageType: "invalid_image_type",
  invalidImageVideoType: "invalid_image_video_type",
  invalidAudioType: "invalid_audio_type",
  imageFileSizeExceed: "image_file_size_exceed",
  audioFileSizeExceed: "audio_file_size_exceed",
};

export const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png"];
export const ACCEPTED_VIDEO_TYPES = ["video/mp4"];
export const ACCEPTED_AUDIO_TYPES = ["audio/mp3", "audio/wma", "audio/mpeg"];
export const MAX_IMAGE_FILE_SIZE = 2000000;
export const MAX_VIDEO_FILE_SIZE = ********;
export const MAX_AUDIO_FILE_SIZE = ********;

export const UNITY_ENDPOINT = "https://room001.fun-verse.io/";

export const SOCIAL_MEDIAS: Record<
  string,
  { label: string; src: string; placeholder: string, method: string }
> = {
  facebook: {
    label: "Facebook",
    src: "/images/icon-facebook.png",
    placeholder: "https://www.facebook.com/funverse",
    method: "facebook"
  },
  instagram: {
    label: "Instagram",
    src: "/images/icon-instagram.png",
    placeholder: "https://instagram.com/funverse",
    method: "instagram"
  },
  linkedin: {
    label: "Linkedin",
    src: "/images/icon-linkedin.png",
    placeholder: "https://www.linkedin.com/funverse",
    method: "linkedin"
  },
  pinterest: {
    label: "Pinterest",
    src: "/images/icon-pinterest.png",
    placeholder: "https://pinterest.com/funverse",
    method: "pinterest"
  },
  tiktok: {
    label: "TikTok",
    src: "/images/icon-tiktok.png",
    placeholder: "https://tiktok.com/@funverse",
    method: "tiktok"
  },
  x: {
    label: "X / Twitter",
    src: "/images/icon-x.png",
    placeholder: "https://x.com/funverse",
    method: "x"
  },
  youtube: {
    label: "Youtube",
    src: "/images/icon-youtube.png",
    placeholder: "https://www.youtube.com/funverse",
    method: "youtube"
  },
  other: {
    label: "Other",
    src: "/images/icon-www.png",
    placeholder: "https://www.example.com",
    method: "other"
  },
};

export const CONTACTS_METHODS: Record<
  string,
  { label: string; src: string; placeholder?: string, method?: string }
> = {
  whatsapp: {
    label: "Whatsapp",
    method: "whatsapp",
    src: "/images/icon-whatsapp.png",
    placeholder: "https://wa.me/85212345678",
  },
  telegram: {
    label: "Telegram",
    method: "telegram",
    src: "/images/icon-telegram.png",
    placeholder: "https://t.me/username",
  },
  phone: {
    label: "Phone Number",
    method: "phone",
    src: "/images/icon-phone.svg",
    placeholder: "85212345678",
  },
  address: {
    label: "Address",
    method: "address",
    src: "/images/icon-address.svg",
    placeholder: "Unit, Floor, Flat, Building, Street, Region, Country",
  },
  email: {
    label: "Email",
    method: "email",
    src: "/images/icon-email.svg",
    placeholder: "<EMAIL>",
  }
};

export const SUPPORT_WEIGTH = [{
  label: "lb",
  value: "lb"
}, {
  label: "oz",
  value: "oz"
}, {
  label: "kg",
  value: "kg"
}, {
  label: "g",
  value: "g"
}]

export const SUPPORT_CURRENCY = [{
  label: "USD",
  value: "USD"
}, {
  label: "HKD",
  value: "HKD"
}, {
  label: "CNY",
  value: "cny"
}, {
  label: "AUS",
  value: "AUS"
}, {
  label: "JPY",
  value: "JPY"
}]
